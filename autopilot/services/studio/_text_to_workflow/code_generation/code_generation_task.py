import copy
import pathlib
import re
from collections import defaultdict

import langchain.chains
import langchain.chat_models
import langchain.embeddings
import langchain.prompts
import langchain.runnables
import langchain.schema
import langchain_community.callbacks
import langchain_core
import langchain_core.runnables
import yaml
from langchain_core.language_models import BaseChatModel

from services.studio._text_to_workflow.code_generation.code_generation_demo_retriever import CodeGenerationDemoRetriever
from services.studio._text_to_workflow.code_generation.code_generation_retriever import CodeGenerationRetriever
from services.studio._text_to_workflow.code_generation.code_generation_schema import (
    CodeGenerationTaskResult,
    CodeMethodDefinition,
    CodePlanStep,
    FixCodeTaskResult,
    UIObject,
)
from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import embedding_model, telemetry_utils
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType, TokenUsage
from services.studio._text_to_workflow.utils.request_utils import get_request_context
from services.studio._text_to_workflow.utils.telemetry_utils import log_execution_time
from services.studio._text_to_workflow.utils.translate.translate_text_task import TranslateTextTask
from services.studio._text_to_workflow.utils.yaml_utils import YAMLError, yaml_dump, yaml_load

LOGGER = telemetry_utils.AppInsightsLogger()
TRANSLATE_TASK = TranslateTextTask("translate_prompt.yaml")


class CodeGenerationTask:
    config_path: pathlib.Path
    config: dict
    embed_model: embedding_model.EmbeddingModel

    def __init__(self, config_name: str) -> None:
        super().__init__()
        self.config_path = (pathlib.Path(__file__).parent / config_name).absolute()
        self.config = yaml_load(self.config_path)
        # embed model
        self.embed_model = ModelManager().get_embeddings_model()

        # retriever
        self.retriever = CodeGenerationRetriever(self.config_path)
        self.demo_retriever = CodeGenerationDemoRetriever()

    def get_db_hash(self) -> str | None:
        return self.retriever.db_hash

    def _build_planning_demonstration(
        self,
        demonstration: dict,
        user_message_template: langchain.prompts.HumanMessagePromptTemplate,
        assistant_message_template: langchain.prompts.AIMessagePromptTemplate,
    ) -> list[langchain.schema.BaseMessage]:
        return [user_message_template.format(query=demonstration["input"]), assistant_message_template.format(plan=demonstration["output"])]

    # TODO: this is very similar to the one from workflow utils, can be replaced?
    def load_yaml(self, output: str) -> dict:
        try:
            if output.startswith("```yaml\n"):
                output = output[8:]
                yaml_end = output.index("```")
                output = output[:yaml_end]
            if not output.startswith("steps\n") and not output.startswith("'steps':\n"):
                output = "steps:\n" + output
            loaded = yaml_load(output)
        except YAMLError:
            LOGGER.warning("YAMLError in output. Trying to fix single quotes.")
            lines = output.splitlines()
            lines_fixed = []
            for line in lines:
                parts = line.split(": ", 1)
                if len(parts) > 1:
                    if not parts[1].strip().startswith("'"):
                        line = f"{parts[0]}: '{parts[1]}"
                    if not parts[1].strip().endswith("'"):
                        line += "'"
                    line = line.replace("\\'", "''")
                lines_fixed.append(line)
            process_str = "\n".join(lines_fixed)
            loaded = yaml_load(process_str)
        return loaded

    @log_execution_time("CodeGenerationPlanning")
    async def plan_code_steps(self, inputs: dict, chat_chain: langchain_core.runnables.RunnableSequence, model: BaseChatModel, can_retry: bool = True) -> dict:
        try:
            with langchain_community.callbacks.get_openai_callback() as cb:
                result = (await chat_chain.ainvoke(inputs)).content
                usage = TokenUsage(
                    model=model.model_name,  # type: ignore
                    prompt_tokens=cb.prompt_tokens,
                    completion_tokens=cb.completion_tokens,
                    total_tokens=cb.total_tokens,
                )

                return {"plan": result, "usage": usage}
        except Exception as e:
            # for parsing errors log the query and the generated plan. try once more
            LOGGER.log_custom_event("CodeGeneration.PlanningParsingError", {"query": inputs.get("query", "")})

            if can_retry:
                return await self.plan_code_steps(inputs=inputs, chat_chain=chat_chain, model=model, can_retry=False)
            else:
                raise e

    def embed_plan_steps(self, plan: str) -> list[CodePlanStep]:
        steps: list[CodePlanStep] = []
        for step in plan.split("\n"):
            splited = step.split(".", 1)
            if len(splited) != 2:
                continue
            _, val = splited
            step = val.strip()
            step_embedding = self.embed_model.encode(step)
            steps.append({"text": step, "embedding": step_embedding.tolist(), "methods": []})
        return steps

    @log_execution_time("CodeGenerationGen")
    async def generate_code(self, inputs: dict, chat_chain: langchain_core.runnables.RunnableSequence, model: BaseChatModel) -> dict:
        with langchain_community.callbacks.get_openai_callback() as cb:
            result = (await chat_chain.ainvoke(inputs)).content
            usage = TokenUsage(
                model=model.model_name,  # type: ignore
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        return {"code": result, "usage": usage}

    def build_type_definitions(self, type_definitions: str, relevant: list[CodeMethodDefinition]):
        type_definitions_collection = [type_definitions]

        additional_type_definitions_hashes = set()
        type_definitions_by_type = defaultdict(lambda: [])
        namespace_imports = set()

        for document in relevant:
            type_definitions_by_type[document["typeFullName"]].append(document["definitions"])

            for namespace_import in document["namespaceImports"].splitlines():
                namespace_imports.add(namespace_import)

            class_definitions = re.findall(r"(class|interface)\s+([\w]+)\s*(\{[^}]+\})", document["additionalTypeDefinitions"])

            additional_type_defs = set()
            for class_def in class_definitions:
                additional_type_defs.add(
                    "{class_def} {class_name} {class_content}".format(class_def=class_def[0], class_name=class_def[1], class_content=class_def[2])
                )

            for additional_type_def in additional_type_defs:
                additional_type_defs_hash = hash(additional_type_def)
                if additional_type_defs_hash not in additional_type_definitions_hashes:
                    additional_type_definitions_hashes.add(additional_type_defs_hash)
                    type_definitions_collection.append(additional_type_def)

        namespaced_type_defs_collection = defaultdict(lambda: [])
        for type_definition in type_definitions_by_type:
            parts = type_definition.split(".")
            activity_name = parts[-1]
            namespace = ".".join(parts[:-1])

            namespaced_type_defs_collection[namespace].append(
                "class {activity_name} {{\r\n{type_definition}\r\n}}".format(
                    activity_name=activity_name, type_definition="\r\n".join(type_definitions_by_type[type_definition])
                )
            )

        for namespaced_type_defs in namespaced_type_defs_collection:
            type_definitions_collection.append(
                "namespace {namespace} {{\r\n{type_definitions}\r\n}}".format(
                    namespace=namespaced_type_defs, type_definitions="\r\n".join(namespaced_type_defs_collection[namespaced_type_defs])
                )
            )

        all_type_definitions = "\r\n".join(type_definitions_collection)
        return list(namespace_imports), all_type_definitions

    async def run(
        self,
        user_request: str,
        type_definitions: str,
        variables: str,
        objects: str | list[UIObject],
        method_name: str | None,
        class_name: str | None,
        target_framework: TargetFramework,
        seed: int | None,
    ) -> CodeGenerationTaskResult:
        # objects
        detailed_objects = False
        if isinstance(objects, str) and objects != "":
            objects = self.config["prompt"]["object_instructions"].format(objects=objects)
        elif isinstance(objects, list):
            objects = self.config["prompt"]["object_descriptors_instructions"].format(
                objects=str(yaml.dump({"Apps": self.convert_ui_object_to_dict(objects)}, sort_keys=False))
            )
            detailed_objects = True

        # run planning
        plan_messages = [langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["plan_system_template"])]
        plan_user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["plan_user_template"])
        plan_assistant_message_template = langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["plan_assistant_template"])

        result = self.demo_retriever.get_demos(type_of_generation="plan_demonstrations", query=user_request, k=4)
        plan_demonstrations = result["demonstrations"]

        # get methods from the relevant plan demonstrations
        relevant_methods = set()
        relevant_docs = []
        for demo in plan_demonstrations:
            parts = demo["output"].split("\n")
            methods = [".".join(p.split(". ")[1:]) for p in parts if p]
            for method in methods:
                if method not in relevant_methods:
                    doc = self.retriever.get_document(target_framework, method)
                    if doc:
                        relevant_methods.add(method)
                        relevant_docs.append(doc)

        for demonstration in plan_demonstrations:
            planning_demonstration = self._build_planning_demonstration(demonstration, plan_user_message_template, plan_assistant_message_template)
            plan_messages.extend(planning_demonstration)
        plan_messages.append(plan_user_message_template)
        plan_chat_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(plan_messages)

        plan_model = ModelManager().get_llm_model("code_generation_retrieval_model", ConsumingFeatureType.CODE_GENERATION)
        plan_chain = plan_chat_prompt_template | plan_model

        inputs = {"query": user_request, "objects": objects}
        planning_prompt = plan_chain.get_prompts()[0].format(**inputs)
        result = await self.plan_code_steps(inputs, plan_chain, plan_model)

        plan = result["plan"]
        planning_usage = result["usage"]

        # retrieve relevant method definitions
        steps = self.embed_plan_steps(plan)
        relevant = self.retriever.get_relevant(steps, target_framework, k=4)

        # add new found relevant methods
        for doc in relevant:
            if doc["typeMethodName"] not in relevant_methods:
                relevant_methods.add(doc["typeMethodName"])
                relevant_docs.append(doc)

        gen_system_message_template = langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["gen_system_template"])
        gen_messages = [gen_system_message_template]

        # retrieve relevant code demonstration files
        result = self.demo_retriever.get_demos(type_of_generation="code_demonstrations", query=user_request, k=4)

        code_demonstrations = result["demonstrations"]

        for code_demonstration in code_demonstrations:
            self.add_generation_demonstration(gen_messages, code_demonstration, detailed_objects)

        # build code location
        code_location = None
        if method_name:
            code_location = "Write the code that implements the requirements. The code is to be added within method {method} in the class {type_name}.".format(
                method=method_name, type_name=class_name
            )
        elif class_name:
            code_location = "Write the code that implements the requirements. The code is to be added within the class {type_name}".format(type_name=class_name)
        else:
            code_location = "Write the code that implements the requirements. The code is to be added within a new file"

        gen_messages.append(langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["gen_user_template"]))  # type: ignore

        gen_prompt = langchain.prompts.ChatPromptTemplate.from_messages(gen_messages)
        gen_model = ModelManager().get_llm_model("code_generation_model", ConsumingFeatureType.CODE_GENERATION)
        gen_chain = gen_prompt | gen_model

        namespace_imports, aggregated_type_definitions = self.build_type_definitions(type_definitions, relevant)
        inputs = {
            "type_definitions": aggregated_type_definitions,
            "objects": objects,
            "code_location": code_location,
            "query": user_request,
            "available_variables": variables,
        }

        code_prompt = gen_chain.get_prompts()[0].format(**inputs)

        result = await self.generate_code(inputs, gen_chain, gen_model)

        pattern = r".*?(```csharp.*?```)"
        code = re.sub(pattern, r"\1\n", result["code"], flags=re.DOTALL)

        # Remove everything after the csharp block and before the next yaml block or the end of the text if yaml block is not present
        pattern = r"(```csharp.*?\n```).*?(?=```yaml|$)"
        code = re.sub(pattern, r"\1\n", code, flags=re.DOTALL)

        # Remove everything after yaml block
        pattern = r"(```yaml.*?```).*"
        code = re.sub(pattern, r"\1", code, flags=re.DOTALL)

        return {
            "useful_methods": [method["typeFullName"] + "." + method["name"] for method in relevant_docs],
            "code": code,
            "imports": namespace_imports,
            "relevant": relevant_docs,
            "planning_prompt": planning_prompt,
            "planning_usage": planning_usage,
            "code_prompt": code_prompt,
            "code_usage": result["usage"],
        }

    def add_generation_demonstration(self, gen_messages, code_demonstration, detailed_objects=False):
        if detailed_objects and code_demonstration["uia_objects_detailed"]:
            objects = self.config["prompt"]["object_descriptors_instructions"].format(objects=code_demonstration["objects_descriptors"])
            output = code_demonstration["output_descriptors"]
        else:
            objects = code_demonstration["objects"]
            if objects != "":
                objects = self.config["prompt"]["object_instructions"].format(objects=objects)
            output = code_demonstration["output"]
        gen_messages.append(
            langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["gen_user_template"]).format(
                code_location=code_demonstration["code_location"],
                query=code_demonstration["query"],
                objects=objects,
                type_definitions=code_demonstration["type_definitions"],
                available_variables=code_demonstration["available_variables"],
            )
        )
        gen_messages.append(
            langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["gen_assistant_template"]).format(
                output=output, packages=yaml_dump({"packages": code_demonstration["packages"]})
            )
        )

    def add_fix_code_demonstrations(self, messages, demonstrations):
        for demonstration in demonstrations:
            errors = self.config["prompt"]["fix_code_error_template"].format(
                error=demonstration["error"]["message"], error_code=demonstration["error"]["code"], line_number=demonstration["error"]["line_number"]
            )
            messages.append(
                langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["fix_code_user_template"]).format(
                    current_code=demonstration["current_code"],
                    errors=errors,
                    type_definitions=demonstration["type_definitions"],
                    objects=demonstration["objects"],
                )
            )
            messages.append(
                langchain.prompts.AIMessagePromptTemplate.from_template(self.config["prompt"]["fix_code_assistant_template"]).format(
                    reasoning=demonstration["reasoning"], fixed_code=demonstration["fixed_code"]
                )
            )

    async def run_fix_code(
        self,
        current_code: str,
        errors: list,
        type_definitions: str,
        objects: str | list[UIObject],
        target_framework: TargetFramework,
        seed: int | None,
    ) -> FixCodeTaskResult:
        config = copy.deepcopy(self.config)
        messages = [langchain.prompts.SystemMessagePromptTemplate.from_template(self.config["prompt"]["fix_code_system_template"])]

        error_messages = "\n".join(error["errorMessage"] for error in errors)
        result = self.demo_retriever.get_demos(
            type_of_generation="fix_code_demonstrations",
            query=error_messages,
            k=3,
        )
        demonstrations = result["demonstrations"]

        self.add_fix_code_demonstrations(messages, demonstrations)

        user_message_template = langchain.prompts.HumanMessagePromptTemplate.from_template(self.config["prompt"]["fix_code_user_template"])
        messages.append(user_message_template)

        chat_prompt_template = langchain.prompts.ChatPromptTemplate.from_messages(messages)

        model = ModelManager().get_llm_model("code_generation_model", ConsumingFeatureType.FIX_CODE)
        if config.get("use_speculative_decoding_for_fix_code", False):
            model = model.bind(
                prediction={
                    "type": "content",
                    "content": current_code,
                }
            )
        chat_chain = chat_prompt_template | model

        errors_message = ",\n".join(
            self.config["prompt"]["fix_code_error_template"].format(error=error["errorMessage"], error_code=error["errorCode"], line_number=error["lineNumber"])
            for error in errors
        )

        inputs = {
            "current_code": current_code,
            "errors": errors_message,
            "type_definitions": type_definitions,
            "objects": str(yaml.dump({"Apps": self.convert_ui_object_to_dict(objects)}, sort_keys=False)),
        }

        with langchain_community.callbacks.get_openai_callback() as cb:
            result = (await chat_chain.ainvoke(inputs)).content
            usage = TokenUsage(
                model=model.model_name,
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )

        match = re.search(r"Analysis:(.*?)\n^\s*```csharp", result, re.DOTALL | re.MULTILINE)

        if match:
            analysis = match.group(1).strip()
            code = result[match.end() :].strip().removesuffix("```").strip()
        else:
            analysis = "We could not infer a potential fix for this error automatically."
            code = current_code

        request_context = get_request_context()
        if request_context is not None and request_context.localization is not None and not request_context.localization.lower().startswith("en"):
            LOGGER.info(f"Translating analysis to {request_context.localization}")
            analysis = await TRANSLATE_TASK.translate_single_str(
                {"target_language": request_context.localization, "input_string": analysis, "feature": ConsumingFeatureType.FIX_CODE}
            )

        return {
            "reasoning": analysis,
            "fixedCode": code,
            "usage": usage,
        }

    def convert_ui_object_to_dict(self, ui_object: list[UIObject] | UIObject) -> dict:
        if isinstance(ui_object, list):
            return [self.convert_ui_object_to_dict(i) for i in ui_object]

        # Define what keys we want for each type
        keys_for_types = {
            "App": ["name", "descriptor"],
            "Screen": ["name", "descriptor"],
            "Element": ["name", "descriptor", "taxonomyType"],
        }

        # Get the keys for the current ui_object
        keys = keys_for_types.get(ui_object["type"], [])

        # Keep only the desired keys
        result = {key: ui_object[key] for key in keys}

        if ui_object.get("children"):
            if ui_object["type"] == "App":
                result["Screens"] = self.convert_ui_object_to_dict(ui_object["children"])
            elif ui_object["type"] == "Screen":
                result["Elements"] = self.convert_ui_object_to_dict(ui_object["children"])
        return result


if __name__ == "__main__":
    import pprint

    task = CodeGenerationTask("prompt.yaml")
    result = task.run("I want to create a new file with the name 'test.txt' and the content 'Hello World!'", "", "", "Insert Code", "", "", "", "Windows", True)
    print("CODE")
    print(result["code"])
    print("RELEVANT")
    pprint.pprint(result["relevant"])
