import copy
import json
from typing import List

import langchain.chains
import langchain.prompts
import langchain.schema
from uipath_cv_client.dom_processing import truncate_dom_str

from services.studio._text_to_workflow.ui_automation.llm_chat_providers import LLMChat
from services.studio._text_to_workflow.ui_automation.qa_semantic.qa_prompt_message import (
    container_id_guidline,
    label_matching_prepend_dom_message,
    label_matching_query_example_list,
    qa_element_query_example_list,
    qa_prompt_system_message,
    qa_user_message,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import ConsumingFeatureType


class UIAQAPrompter:
    """Builds prompts for QA tasks"""

    def __init__(self, prompt_options: dict):
        # set default value
        self.prompt_config = {
            "max_dom_tokens": 4000,
            "trimmable": None,
            "completion_start": "",
            "completion_stop": "",
            "max_url_size": 150,
        }
        self.prompt_config.update(prompt_options)

    def build_qa_element_prompt_with_llmgw(
        self,
        dom_str: str,
        human_element_description: str,
        image_base64: str | None = None,
    ) -> list[dict]:
        messages = []
        system_message = dict(role="system", content=qa_prompt_system_message)
        messages.append(system_message)
        for example in qa_element_query_example_list:
            user_message_content = qa_user_message.format(dom_string=example[0], human_description=example[1])
            user_message = dict(role="user", content=user_message_content)
            messages.append(user_message)
            assistant_message_content = example[2]
            assistant_message = dict(role="assistant", content=assistant_message_content)
            messages.append(assistant_message)

        user_message_content = [
            {
                "type": "text",
                "text": qa_user_message.format(dom_string=dom_str, human_description=human_element_description),
            }
        ]
        if image_base64:
            user_message_content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                }
            )
        user_message = dict(role="user", content=user_message_content)
        messages.append(user_message)

        return messages

    def build_qa_element_prompt(
        self,
        dom_string,
        human_description: str,
        image_base64: str | None,
        add_label_matching_examples: bool = False,
        use_container_id: bool = False,
    ) -> List[langchain.schema.BaseMessage]:
        examples = copy.deepcopy(qa_element_query_example_list)
        prepend_dom_message = ""
        if add_label_matching_examples:
            examples += label_matching_query_example_list
            prepend_dom_message = label_matching_prepend_dom_message

        messages = []

        system_prompt_message = qa_prompt_system_message.format(container_id_guidline=container_id_guidline if use_container_id else "")
        system_message = langchain.schema.SystemMessage(role="system", content=system_prompt_message)
        messages.append(system_message)
        for example in examples:
            user_message_content = qa_user_message.format(dom_string=example[0], human_description=example[1])
            user_message = langchain.schema.HumanMessage(role="user", content=user_message_content)
            messages.append(user_message)
            assistant_message_content = example[2]
            assistant_message = langchain.schema.AIMessage(role="assistant", content=assistant_message_content)
            messages.append(assistant_message)

        user_message_content = [
            {
                "type": "text",
                "text": qa_user_message.format(
                    dom_string=prepend_dom_message + dom_string,
                    human_description=human_description,
                ),
            }
        ]
        if image_base64:
            user_message_content.append(
                {
                    "type": "image_url",
                    "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"},
                }
            )
        user_message = langchain.schema.HumanMessage(role="user", content=user_message_content)
        messages.append(user_message)

        return messages


class QAModel:
    def __init__(self, options: dict):
        self.options = options
        self.qa_prompter = UIAQAPrompter(options.get("prompt_options", {}))

    async def qa_element(
        self,
        dom_str: str,
        human_element_description: str,
        image_base64: str | None = None,
        add_label_matching_examples: bool = False,
        use_container_id: bool = False,
        consuming_feature_type: ConsumingFeatureType = ConsumingFeatureType.QA_ELEMENT,
    ) -> int | None:
        dom_str = truncate_dom_str(dom_str, max_dom_tokens=self.options.get("prompt_options", {}).get("max_dom_tokens", 4000))

        qa_element_messages = self.qa_prompter.build_qa_element_prompt(
            dom_str, human_element_description, image_base64, add_label_matching_examples, use_container_id
        )
        gpt_chat = LLMChat(self.options, consuming_feature_type=consuming_feature_type)
        predict_info = {}
        out = await gpt_chat.send_message(qa_element_messages, predict_info, max_rate_limit_retries=0, engine_config_key="semantic_targeting_default_engine")
        out_dict = json.loads(out.content.strip().removeprefix("```json").removesuffix("```").strip())
        element_id = out_dict.get("Id")
        if element_id is not None:
            element_id = int(element_id)

        return element_id
