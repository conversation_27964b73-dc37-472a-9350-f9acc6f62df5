from unittest.mock import patch

import pytest

from services.studio._text_to_workflow.utils.formatting_utils import LOGGER, FormattingMissingKeysAwareDict, format_partially, format_recursively


@pytest.mark.unit
class TestFormattingMissingKeysAwareDict:
    """Test cases for FormattingMissingKeysAwareDict class."""

    def test_init_with_args(self):
        """Test initialization with positional arguments."""
        test_dict = FormattingMissingKeysAwareDict([("key1", "value1"), ("key2", "value2")])
        assert test_dict["key1"] == "value1"
        assert test_dict["key2"] == "value2"

    def test_init_with_kwargs(self):
        """Test initialization with keyword arguments."""
        test_dict = FormattingMissingKeysAwareDict(key1="value1", key2="value2")
        assert test_dict["key1"] == "value1"
        assert test_dict["key2"] == "value2"

    def test_missing_key_returns_formatted_key(self):
        """Test that missing keys return formatted key string."""
        test_dict = FormattingMissingKeysAwareDict({"existing": "value"})
        assert test_dict["missing_key"] == "{missing_key}"

    def test_missing_key_logs_warning(self):
        """Test that missing keys trigger a warning log."""
        with patch.object(LOGGER, "warning") as mock_warning:
            test_dict = FormattingMissingKeysAwareDict(issue_warning=True)
            _ = test_dict["missing_key"]
            mock_warning.assert_called_once()

    def test_missing_key_with_special_characters(self):
        """Test missing keys with special characters."""
        test_dict = FormattingMissingKeysAwareDict()
        assert test_dict["key_with_underscores"] == "{key_with_underscores}"
        assert test_dict["key-with-dashes"] == "{key-with-dashes}"
        assert test_dict["key with spaces"] == "{key with spaces}"


@pytest.mark.unit
class TestFormatRecursively:
    """Test cases for format_recursively function."""

    def test_simple_formatting(self):
        """Test basic string formatting."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice", "age": "25"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, you are 25 years old."

    def test_formatting_with_kwargs(self):
        """Test formatting with kwargs."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice"}
        result = format_recursively(template, values, age="25")
        assert result == "Hello Alice, you are 25 years old."

    def test_kwargs_only(self):
        """Test formatting with kwargs only."""
        template = "Hello {name}, you are {age} years old."
        result = format_recursively(template, age="25")
        assert result == "Hello {name}, you are 25 years old."

    def test_kwargs_override_values(self):
        """Test that kwargs override values dict."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice", "age": "25"}
        result = format_recursively(template, values, age="30")
        assert result == "Hello Alice, you are 30 years old."

    def test_missing_keys_preserved(self):
        """Test that missing keys are preserved in output."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, you are {age} years old."

    def test_recursive_formatting(self):
        """Test recursive formatting with nested placeholders."""
        template = "Hello {name}, your full name is {full_name}."
        values = {"name": "Alice", "full_name": "{name} Smith"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, your full name is Alice Smith."

    def test_deep_recursive_formatting(self):
        """Test deep recursive formatting."""
        template = "Hello {name}, your info is {info}."
        values = {
            "name": "Alice",
            "info": "{details}",
            "details": "{personal_info}",
            "personal_info": "Age: {age}, City: {city}",
            "age": "25",
            "city": "New York",
        }
        result = format_recursively(template, values)
        assert result == "Hello Alice, your info is Age: 25, City: New York."

    def test_max_depth_limit(self):
        """Test that formatting stops at max_depth."""
        template = "{value}"
        values = {
            "value": "{value2}",
            "value2": "{value3}",
            "value3": "{value4}",
            "value4": "end",
        }
        result = format_recursively(template, values, max_depth=3)
        assert result == "{value4}"  # Should stop at max_depth, not resolve the circular reference

    def test_max_depth_warning_logged(self):
        """Test that warning is logged when max_depth is reached."""
        template = "{value}"
        values = {"value": "end"}
        with patch.object(LOGGER, "warning") as mock_warning:
            format_recursively(template, values, max_depth=1)
            mock_warning.assert_called_once()

    def test_idempotency_check(self):
        """Test that formatting stops when no more changes occur."""
        template = "Hello {name}!"
        values = {"name": "Alice"}
        result = format_recursively(template, values)
        assert result == "Hello Alice!"

    def test_empty_template(self):
        """Test formatting with empty template."""
        template = ""
        values = {"name": "Alice"}
        result = format_recursively(template, values)
        assert result == ""

    def test_empty_values(self):
        """Test formatting with empty values dict."""
        template = "Hello {name}!"
        values = {}
        result = format_recursively(template, values)
        assert result == "Hello {name}!"

    def test_none_values(self):
        """Test formatting with None values."""
        template = "Hello {name}, age: {age}!"
        values = {"name": None, "age": "25"}
        result = format_recursively(template, values)
        assert result == "Hello None, age: 25!"

    def test_special_characters_in_keys(self):
        """Test formatting with special characters in keys."""
        template = "Hello {user_name}, your ID is {user-id}."
        values = {"user_name": "Alice", "user-id": "12345"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, your ID is 12345."

    def test_unicode_characters(self):
        """Test formatting with unicode characters."""
        template = "Hello {name}, your message is: {message}"
        values = {"name": "José", "message": "¡Hola mundo!"}
        result = format_recursively(template, values)
        assert result == "Hello José, your message is: ¡Hola mundo!"

    def test_numeric_values(self):
        """Test formatting with numeric values."""
        template = "Count: {count}, Price: {price}"
        values = {"count": 42, "price": 19.99}
        result = format_recursively(template, values)
        assert result == "Count: 42, Price: 19.99"

    def test_boolean_values(self):
        """Test formatting with boolean values."""
        template = "Active: {active}, Enabled: {enabled}"
        values = {"active": True, "enabled": False}
        result = format_recursively(template, values)
        assert result == "Active: True, Enabled: False"

    def test_complex_nested_formatting(self):
        """Test complex nested formatting scenario."""
        template = """
        User: {user_info}
        Settings: {settings}
        """
        values = {
            "user_info": "{name} ({email})",
            "name": "Alice",
            "email": "<EMAIL>",
            "settings": "{theme} theme, {language} language",
            "theme": "dark",
            "language": "English",
        }
        result = format_recursively(template, values)
        expected = """
        User: Alice (<EMAIL>)
        Settings: dark theme, English language
        """
        assert result == expected

    def test_formatting_with_braces_in_values(self):
        """Test formatting when values contain braces."""
        template = "Message: {message}"
        values = {"message": "This contains {curly} braces"}
        result = format_recursively(template, values)
        assert result == "Message: This contains {curly} braces"

    def test_formatting_with_escaped_braces(self):
        """Test formatting with escaped braces in template."""
        template = "Price: ${price}, Note: {{This is literal}}"
        values = {"price": "19.99"}
        result = format_recursively(template, values)
        assert result == "Price: $19.99, Note: {This is literal}"

    def test_escaping_braces_with_existing_key(self):
        """Test formatting with multiple escaping braces in template."""
        template = "Hello {name}, This is a literal {{name}}"
        values = {"name": "Alice"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, This is a literal {name}"

    def test_multiple_escaped_braces_depth(self):
        """Test formatting with multiple escaped braces in template."""
        template = "Hello {name}, These are literals {{{{name}}}} {{{{{{name}}}}}}"
        values = {
            "name": "{name1}",
            "name1": "{name2}",
            "name2": "{name3}",
            "name3": "{name4}",
        }
        for max_depth in [1, 2, 3]:
            result = format_recursively(template, values, max_depth=max_depth)
            assert result.endswith("These are literals {{name}} {{{name}}}")

    def test_escaped_and_unescaped_braces(self):
        """Test formatting with escaped and unescaped braces in template."""
        template = "Hello {name}, This is a literal {{{name}}}"
        values = {"name": "{name2}", "name2": "Alice"}
        result = format_recursively(template, values)
        assert result == "Hello Alice, This is a literal {Alice}"


@pytest.mark.unit
class TestFormatPartially:
    """Test cases for format_partially function."""

    def test_simple_formatting(self):
        """Test basic string formatting with existing keys."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice", "age": "25"}
        result = format_partially(template, values)
        assert result == "Hello Alice, you are 25 years old."

    def test_formatting_with_kwargs(self):
        """Test formatting with kwargs."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice"}
        result = format_partially(template, values, age="25")
        assert result == "Hello Alice, you are 25 years old."

    def test_kwargs_only(self):
        """Test formatting with kwargs only."""
        template = "Hello {name}, you are {age} years old."
        result = format_partially(template, age="25")
        assert result == "Hello {name}, you are 25 years old."

    def test_kwargs_override_values(self):
        """Test that kwargs override values dict."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice", "age": "25"}
        result = format_partially(template, values, age="30")
        assert result == "Hello Alice, you are 30 years old."

    def test_missing_keys_preserved(self):
        """Test that missing keys are preserved with braces in output."""
        template = "Hello {name}, you are {age} years old."
        values = {"name": "Alice"}
        result = format_partially(template, values)
        assert result == "Hello Alice, you are {age} years old."

    def test_mixed_existing_and_missing_keys(self):
        """Test formatting with mix of existing and missing keys."""
        template = "User: {name}, Age: {age}, City: {city}, Country: {country}"
        values = {"name": "Alice", "city": "New York"}
        result = format_partially(template, values)
        assert result == "User: Alice, Age: {age}, City: New York, Country: {country}"

    def test_empty_template(self):
        """Test formatting with empty template."""
        template = ""
        values = {"name": "Alice"}
        result = format_partially(template, values)
        assert result == ""

    def test_empty_values(self):
        """Test formatting with empty values dict."""
        template = "Hello {name}!"
        values = {}
        result = format_partially(template, values)
        assert result == "Hello {name}!"

    def test_escaped_braces_preservation(self):
        """Test that escaped braces are preserved in output."""
        template = "Hello {name}, This is a literal {{{name}}}. This one too {{x}}"
        values = {"name": "Alice"}
        result = format_partially(template, values)
        assert result == "Hello Alice, This is a literal {{Alice}}. This one too {{x}}"
