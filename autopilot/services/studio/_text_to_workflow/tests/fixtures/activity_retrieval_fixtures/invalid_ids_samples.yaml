hallucinated_ids: # check that hallucinated ids are removed
  proposed_activities:
    - DummyActivity1
    - DummyActivity2
    - DummyActivity3
  proposed_triggers:
    - DummyTrigger4
    - DummyTrigger5
  retrieved_trigger_ids: [9999]
  retrieved_activity_ids: [9998]
  expected_triggers: []
  expected_activities: []
trigger_and_activities_mixup: # check that trigger ids are returned as activities and vice versa are handled correctly
  proposed_activities:
    - DummyActivity1
    - DummyActivity2
    - DummyActivity3
  proposed_triggers:
    - DummyTrigger4
    - DummyTrigger5
  retrieved_trigger_ids: [2, 3] # these are the ids of the proposed activities
  retrieved_activity_ids: [5] # this is the id of the proposed trigger
  expected_triggers:
    - DummyTrigger5
  expected_activities:
    - DummyActivity2
    - DummyActivity3
