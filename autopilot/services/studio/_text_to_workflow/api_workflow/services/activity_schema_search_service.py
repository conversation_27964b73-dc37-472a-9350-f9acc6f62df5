import copy
import re

import httpx
import langchain_community
import langchain_community.callbacks
import openai
import yaml
from fastapi import HTTPException

from services.studio._text_to_workflow.common.api_workflow.schema import PerplexitySearchOutputConfig
from services.studio._text_to_workflow.core.config import settings
from services.studio._text_to_workflow.utils.errors import UnprocessableEntityError
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.paths import get_perplexity_search_config_path, get_perplexity_search_prompt_path
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load

LOGGER = AppInsightsLogger()


class ActivitySchemaSearchService:
    def __init__(self):
        self.config_path = get_perplexity_search_config_path()
        self.config = yaml_load(self.config_path)
        self.api_url = self.config["perplexity_api_url"]
        self.perplexity_api_key = settings.PERPLEXITY_API_KEY
        self.prompt_config = get_perplexity_search_prompt_path()
        self.timeout = self.config["timeout"]
        with open(self.prompt_config, "r") as f:
            self.prompt = yaml_load(f)

    def get_messages(self, search_request: str, output_schema: dict):
        messages = []
        messages.append({"role": "system", "content": self.prompt["system_msg_template"].format(output_schema=yaml_dump(output_schema))})
        messages.append({"role": "user", "content": self.prompt["user_msg_template"].format(api_name=search_request)})
        return messages

    def validate_search_answer(self, search_answer: str):
        try:
            yaml_pattern = re.compile(
                r"(?:^```(?:ya?ml)?\n?(?P<yaml_full>.*?)```$)|"
                r"(?:^```(?:ya?ml)?\n?(?P<yaml_start>.*)$)|"
                r"(?:^(?P<yaml_end>.*?)```$)|"
                r"(?P<yaml_none>.*)",
                re.DOTALL | re.MULTILINE,
            )

            match = yaml_pattern.search(search_answer.strip())
            if match:
                yaml_content = match.group("yaml_full") or match.group("yaml_start") or match.group("yaml_end") or match.group("yaml_none")
                return yaml_content.strip() if yaml_content else search_answer.strip()
            else:
                return search_answer.strip()
        except Exception:
            return search_answer.strip()

    async def send_llm_request(self, messages, model) -> tuple[PerplexitySearchOutputConfig, TokenUsage]:
        with langchain_community.callbacks.get_openai_callback() as cb:
            try:
                result = await model.ainvoke(messages)
            except openai.LengthFinishReasonError:
                raise UnprocessableEntityError("The generated answer is too large to process. Please try again with a more refined prompt.")
            usage = TokenUsage(
                model="",
                prompt_tokens=cb.prompt_tokens,
                completion_tokens=cb.completion_tokens,
                total_tokens=cb.total_tokens,
            )
        return result, usage

    async def validate_answer_format(self, search_answer: str) -> str | None:
        try:
            search_answer = search_answer.strip()
            search_answer = self.validate_search_answer(search_answer)
            yaml_load(search_answer)
        except yaml.YAMLError:
            search_answer = search_answer + '"'
            try:
                yaml_load(search_answer)
            except yaml.YAMLError:
                LOGGER.error(f"YAML format error in search answer: {search_answer}")
                return None
        return search_answer

    async def web_search(self, search_request: str):
        messages = self.get_messages(search_request, PerplexitySearchOutputConfig.model_json_schema())
        request_body = copy.deepcopy(self.config["model_config"])
        request_body["messages"] = messages
        transport = httpx.AsyncHTTPTransport(retries=self.config["model_config"]["max_retries"])
        timeout = httpx.Timeout(self.timeout)
        try:
            async with httpx.AsyncClient(transport=transport, timeout=timeout) as client:
                response = await client.post(
                    self.api_url,
                    headers={"Authorization": f"Bearer {self.perplexity_api_key}"},
                    json=request_body,
                )
                response.raise_for_status()
                result = response.json()
                return {
                    "status_code": response.status_code,
                    "result": await self.validate_answer_format(result["choices"][0]["message"]["content"]),
                }
        except Exception as e:
            status_code = getattr(e, "response", None)
            if status_code and hasattr(status_code, "status_code"):
                status_code = status_code.status_code
            else:
                status_code = 500
            error_detail = {"error": str(e)}
            raise HTTPException(status_code=status_code, detail=error_detail) from e
