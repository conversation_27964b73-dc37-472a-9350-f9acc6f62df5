import re

from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()

# regex to find keys in a template that are not escaped (odd number of braces before and after)
TEMPLATE_KEY_RE = re.compile(r"(?:^|[^{])(?:\{\{)*\{(?P<key>[^{}]*?)(?:\}\})*\}(?:$|[^}])")


class FormattingMissingKeysAwareDict(dict):
    """
    Missing keys will be returned as "{key}".
    """

    def __init__(self, *args, issue_warning: bool = False, **kwargs):
        super().__init__(*args, **kwargs)
        self.issue_warning = issue_warning

    def __missing__(self, key):
        if self.issue_warning:
            LOGGER.warning(f'While formatting, key "{key}" did not have a replacement in the dictionary.')
        return f"{{{key}}}"


def format_recursively(template: str, values: dict | None = None, max_depth: int = 10, **kwargs) -> str:
    """
    Coalesces values and kwargs into a single dictionary and recursively formats the template with the resulting dictionary.
    Missing keys will be kept as is, without any processing (including the braces).
    """
    coalesced_values = FormattingMissingKeysAwareDict({**(values or {}), **kwargs}, issue_warning=True)
    formatted_string = template
    for _ in range(max_depth):
        _template = formatted_string.replace("{{", "{{{{").replace("}}", "}}}}")  # protect escaping against recursive formatting
        try:
            deeper_formatted_string = _template.format_map(coalesced_values)
        except ValueError as e:
            if "Max string recursion exceeded" in str(e):
                debugging_trace = _debug_recursive_keys(_template, coalesced_values)
                raise ValueError(f"Max string recursion exceeded while formatting. The template was: {_template}\n\n{debugging_trace}") from e
            raise e
        if deeper_formatted_string == formatted_string:  # idempotency check
            break
        formatted_string = deeper_formatted_string
    else:
        LOGGER.warning(f"While formatting, the template converged for all {max_depth} iterations. There might be unresolved keys in the resulting string.")
    return unescape_braces(formatted_string)  # manual processing of escaped braces


def _debug_recursive_keys(template: str, values: FormattingMissingKeysAwareDict) -> str:
    debugging_message = []
    for key in TEMPLATE_KEY_RE.findall(template):
        debugging_message.append(f"Key: {{{key}}}" + " (missing)" * (key not in values))
        for inner_key in TEMPLATE_KEY_RE.findall(values[key]):
            debugging_message.append(f"  Inner key: {{{inner_key}}}")
    return "\n".join(debugging_message)


def format_partially(template: str, values: dict | None = None, **kwargs) -> str:
    """
    Formats the template with the values and kwargs, but only replaces the keys that are present in the values dictionary, keeping the rest as is.
    Note that this is intended only to be used as intermediate step in the formatting process, and not as a final formatting step.
    It will not reduce the protected braces. e.g. `{{key}}` will not be reduced to `{key}`.
    """
    return template.replace("{{", "{{{{").replace("}}", "}}}}").format_map(FormattingMissingKeysAwareDict({**(values or {}), **kwargs}))


def escape_braces(string: str) -> str:
    return string.replace("{", "{{").replace("}", "}}")


def unescape_braces(string: str) -> str:
    return string.replace("{{", "{").replace("}}", "}")


def escape_quotes(s):
    s = re.sub(r"(?<!')'(?!')", "''", s)
    s = re.sub(r'(?<!")"(?!")', '""', s)
    return s.strip()


def unescape_quotes(s):
    s = re.sub(r"(?<!')''(?!')", "'", s)
    s = re.sub(r'(?<!")""(?!")', '"', s)
    return s.strip()
