import collections
import copy
import itertools
import random
import re

import typing_extensions as t
from overrides import override

from services.studio._text_to_workflow.activity_config import activity_config_schema
from services.studio._text_to_workflow.common import constants, params, schema, typedefs, typedefs_parser
from services.studio._text_to_workflow.common.activity_retriever import _DEPRECATED_ACTIVITIES_FOUND_IN_DATASET, ActivitiesRetriever
from services.studio._text_to_workflow.common.helpers import extract_generic_pattern
from services.studio._text_to_workflow.common.schema import (
    ActivityDict,
    ParamTypeCategory,
    ParamValueCategory,
    TargetFramework,
    TypeDef,
    Variable,
    WorkflowDict,
    WorkflowVariables,
)
from services.studio._text_to_workflow.common.workflow import Activity, Workflow
from services.studio._text_to_workflow.utils import errors, formatting_utils, telemetry_utils

LOGGER = telemetry_utils.AppInsightsLogger()


class Walker:
    """
    Walker interface for traversing workflows.

    Override the processing methods to implement the desired behavior.
    """

    def process_activity(self, activity: Activity) -> None:
        pass

    def before_processing_activity_tree(self, activity: Activity) -> None:
        pass

    def after_processing_activity_tree(self, activity: Activity) -> None:
        pass

    def walk_activity(self, activity: Activity) -> None:
        self.process_activity(activity)
        self.before_processing_activity_tree(activity)
        for sequence_container in (activity.activities, activity.activity_delegates):
            for sequence in sequence_container.values():
                self.walk_sequence(sequence)
        self.after_processing_activity_tree(activity)

    def process_sequence(self, sequence: list[Activity], *args) -> None:
        pass

    def before_processing_sequence_forest(self, sequence: list[Activity], *args) -> None:
        pass

    def after_processing_sequence_forest(self, sequence: list[Activity], *args) -> None:
        pass

    def walk_sequence(self, sequence: list[Activity], *args) -> None:
        self.process_sequence(sequence, *args)
        self.before_processing_sequence_forest(sequence, *args)
        for child_activity in sequence:
            self.walk_activity(child_activity)
        self.after_processing_sequence_forest(sequence, *args)

    def process_workflow(self, workflow: Workflow) -> None:
        pass

    def before_processing_workflow_tree(self, workflow: Workflow) -> None:
        pass

    def after_processing_workflow_tree(self, workflow: Workflow) -> None:
        pass

    def walk_workflow(self, workflow: Workflow) -> None:
        self.process_workflow(workflow)
        self.before_processing_workflow_tree(workflow)
        if workflow.trigger is not None:
            self.walk_activity(workflow.trigger)
        self.walk_sequence(workflow.activities)
        self.after_processing_workflow_tree(workflow)

    def walk(self, workflow: Workflow):  # TODO: we could use typing_extensions's Self annotation for return type
        self.walk_workflow(workflow)
        return self


class SyntheticStepsWalker(Walker):
    """
    This walker adds intermediate nodes during traversal for activities that have multiple child sequences.
    This walker also adds a manual trigger if the workflow does not have one.

    For example, an If being an activity with many child sequences would become:
    ```
    If
        Then - synthetic step added
            activity1
        Else - synthetic step added
            activity2
    ```

    Override the processing methods to implement the desired behavior.
    """

    SYNTHETIC_STEP_ACTIVITY_ID = "Synthetic.Step"
    KEYS_TO_SKIP_SYNTHETIC_STEPS = {"Then", "ElseIfs"}
    ACTIVITIES_ALWAYS_INCLUDE_SYNTHETIC_STEPS = {"System.Activities.Statements.Switch"}

    def __init__(self, *args, force_trigger_if_missing=True, **kwargs):
        super().__init__()
        self.force_trigger_if_missing = force_trigger_if_missing

    @override
    def walk_activity(self, activity: Activity) -> None:
        self.before_processing_activity_tree(activity)
        self.process_activity(activity)
        for sequence_container in (activity.activities, activity.activity_delegates):
            for key, sequence in sequence_container.items():
                self.walk_sequence(sequence, key, len(sequence_container), activity)
        self.after_processing_activity_tree(activity)

    @override
    def walk_sequence(
        self,
        sequence: list[Activity],
        key: str | None = None,
        n_sequences: int = 1,
        activity: Activity | None = None,
        *args,
    ) -> None:
        """activity and key can be None for the root sequence"""
        self.process_sequence(sequence, key, n_sequences, activity, *args)
        self.before_processing_sequence_forest(sequence, key, n_sequences, activity, *args)
        if (key is not None and activity is not None) and (
            n_sequences > 1
            and key not in SyntheticStepsWalker.KEYS_TO_SKIP_SYNTHETIC_STEPS
            or activity.activity_id in SyntheticStepsWalker.ACTIVITIES_ALWAYS_INCLUDE_SYNTHETIC_STEPS
        ):
            dummy_activity = Activity(
                lmyaml={"thought": key, "activity": SyntheticStepsWalker.SYNTHETIC_STEP_ACTIVITY_ID, "params": {}},
                workflow_arguments=activity.workflow_arguments,
                workflow_variables=activity.workflow_variables,
                parent=activity,
            )
            dummy_activity.activities = {"Activities": sequence}
            self.walk_activity(dummy_activity)
        else:
            for child_activity in sequence:
                self.walk_activity(child_activity)
        self.after_processing_sequence_forest(sequence, key, n_sequences, activity, *args)

    @override
    def walk_workflow(self, workflow: Workflow) -> None:
        self.process_workflow(workflow)
        self.before_processing_workflow_tree(workflow)
        if workflow.trigger is not None:
            self.walk_activity(workflow.trigger)
        elif self.force_trigger_if_missing:
            self.walk_activity(
                Activity(
                    {"thought": "Manual Trigger", "activity": "UiPath.Core.Activities.ManualTrigger", "params": {}},
                    workflow.arguments,
                    workflow.variables,
                    None,
                )
            )
        self.walk_sequence(workflow.activities)  # use default key, n_sequences, activity (None, 1, None)
        self.after_processing_workflow_tree(workflow)


class PlanBuilder(SyntheticStepsWalker):
    """This walker is responsible for building a plan from a workflow

    A special case for ifs (and ifelseifs)
    ```
    This                                  would become
    ----------------------------------    ----------------------------------
    if                                    if
        then - deindent, exclude              act ...
            act ...                       elseif1
        elseifs - deindent, exclude           act ...
            elseif1 - deindent            elseif2
                act ...                       act ...
            elseif2                       else
                act ...                       act ...
        else - deindent
            act ...
    ```

    Note: When using for sequences, please set the `force_trigger_if_missing` param to False
    """

    KEYS_TO_EXCLUDE_INDENTATION = {"ElseIfs", "Else"}

    _level: int
    _counters: list[int]
    plan: list[str]

    def __init__(self, force_trigger_if_missing=True):
        super().__init__(force_trigger_if_missing=force_trigger_if_missing)
        self._init()

    def _init(self):
        self.plan = []
        self._level = 0
        self._counters = [0]

    def _update_counters(self):
        if len(self._counters) == self._level:
            self._counters.append(1)
        elif len(self._counters) > self._level:
            self._counters = self._counters[: self._level + 1]
            self._counters[-1] += 1
        return self._counters

    def add_plan_step(self, display_name: str) -> None:
        self._update_counters()
        prefix = "  " * self._level
        plan_step_number = ".".join(map(str, self._counters))
        plan_step = f"{prefix}{plan_step_number}. {display_name}"
        self.plan.append(plan_step)

    @override
    def process_activity(self, activity: Activity) -> None:
        display_name = formatting_utils.escape_quotes(activity.display_name.strip())
        self.add_plan_step(display_name)

    @override
    def before_processing_sequence_forest(self, sequence: list[Activity], key: str | None = None, *args) -> None:
        if key is not None and key not in PlanBuilder.KEYS_TO_EXCLUDE_INDENTATION:
            self._level += 1

    @override
    def after_processing_sequence_forest(self, sequence: list[Activity], key: str | None = None, *args) -> None:
        if key is not None and key not in PlanBuilder.KEYS_TO_EXCLUDE_INDENTATION:
            self._level -= 1

    def build(self, wf: Workflow) -> str:
        self._init()
        self.walk_workflow(wf)
        return "\n".join(self.plan)


class SequencePlanBuilder(PlanBuilder):
    """This one is building a plan when applied on a sequence - without trigger"""

    def __init__(self):
        super().__init__(force_trigger_if_missing=False)


class SynopsisBuilder(PlanBuilder):
    """This walker is responsible for building a summary from a workflow"""

    def __init__(
        self,
        activity_typedefs: dict[str, TypeDef] | None = None,
        available_variables: set[str] | None = None,
    ):
        super().__init__()
        self.activity_typedefs = activity_typedefs or {}
        self.available_variables = available_variables or set()
        self.activity_retriever = ActivitiesRetriever()

    @override
    def process_workflow(self, workflow: Workflow) -> None:
        workflow_activity_typedefs, _ = FillMissingWorkflowActivityTypedefs(workflow, self.activity_typedefs, {}, strict_mode=False).fill()
        workflow_variables = CollectWorkflowVariables(workflow, workflow_activity_typedefs, {}, strict_mode=False).collect()
        self.available_variables |= set(itertools.chain.from_iterable(workflow_variables.values()))

    @override
    def process_activity(self, activity: Activity) -> None:
        if activity.activity_id == SyntheticStepsWalker.SYNTHETIC_STEP_ACTIVITY_ID:
            return self.add_plan_step(activity.display_name)
        if activity.activity_id == "System.Activities.Statements.If":
            return self.add_plan_step(f"If {activity.primitive_properties['Condition'].removeprefix('[[').removesuffix(']]')}")
        if activity.activity_id == "UiPath.Core.Activities.LogMessage":
            return self.add_plan_step(f'LogMessage "{activity.primitive_properties["Message"]}"')
        if activity.activity_id == "UiPath.Core.Activities.Assign":
            if "To" in activity.primitive_properties and "Value" not in activity.primitive_properties:  # TODO: temporary until fixed
                print(f"WARNING! Assign activity missing Value {activity.id}")
                activity.primitive_properties["Value"] = ""
            assert "To" in activity.primitive_properties and "Value" in activity.primitive_properties, f"Assign activity missing To or Value {activity.id}"
            var = activity.primitive_properties["To"].removeprefix("[[").removesuffix("]]")
            assert re.match(r"\w+", var) is not None, f"Assign activity To property is not a variable [[{var}]]"
            return self.add_plan_step(f"{var} <- {activity.primitive_properties['Value'].removeprefix('[[').removesuffix(']]')}")
        if activity.activity_id == "UiPath.UIAutomationNext.Activities.NApplicationCard":
            prompt = activity.params.get("AutoGenerationOptions", {}).get("Prompt", "")
            expected_out: list[str] = activity.params.get("AutoGenerationOptions", {}).get("ExpectedOutputs", [])
            expected_out_str: str = ", ".join(v.removeprefix("[[").removesuffix("]]") for v in expected_out)
            return self.add_plan_step(f'{expected_out_str} <- NApplicationCard("{prompt}")')

        out_vars = set()
        for variable in activity.local_variables.values():  # e.g. for each scope
            for var in variable.values():
                out_vars.add(var["name"])
        params_typedefs = {}
        if typedefs.activity_typedef_exists(activity.activity_id):
            params_typedefs = typedefs.get_activity_typedef(activity.activity_id)["params"]
        else:
            print(f"WARNING! No global typedef found for {activity.activity_id}")
            if activity.activity_id in self.activity_typedefs:
                params_typedefs = self.activity_typedefs[activity.activity_id]["params"]
            else:
                print(f"WARNING! Local typedef not found for {activity.activity_id}")

        for param_key, param_value in activity.primitive_properties.items():
            param_typedef = params_typedefs.get(param_key)
            if param_typedef is not None and param_typedef["category"] == "OutArgument":
                out_vars.add(param_value.removeprefix("[[").removesuffix("]]"))

        in_vars = set()
        for expression in re.findall(r"(?<=\[\[).*?(?=\]\])", activity.lmyaml(inhibit_child_activity_expansion=True)):  # iterate all "[[<expression>]]"
            for var in re.findall(r"\w+", expression):  # iterate all words within expression
                if var in self.available_variables and var not in out_vars:
                    in_vars.add(var)

        summary = ""
        if out_vars:
            summary = ", ".join(out_vars) + " <- "
        activity_info = self.activity_retriever.get(activity.activity_id, "Windows")

        if activity.activity_id not in _DEPRECATED_ACTIVITIES_FOUND_IN_DATASET:
            # assert activity_info is not None, f"Activity info not found for {activity.activity_id}"
            if activity_info is None:
                print(f"WARNING! Activity info not found for {activity.activity_id}")
                activity_info = {"fullClassName": activity.activity_id}
            summary += activity_info["fullClassName"]
        else:
            summary += _DEPRECATED_ACTIVITIES_FOUND_IN_DATASET[activity.activity_id]
        if in_vars:
            summary += " (" + ", ".join(in_vars) + ")"
        # summary += f' ["{activity.display_name}"]'
        self.add_plan_step(summary)


class DAPClassNameTranslator(Walker):
    """This walker is responsible for translating DAPs to the full class name"""

    def __init__(self):
        self.activity_retriever = ActivitiesRetriever()

    @override
    def process_activity(self, activity: Activity) -> None:
        if not activity.is_dynamic:
            return
        activity_info = self.activity_retriever.get(activity.activity_id, "Windows")  # Windows is a superset of Portable
        if activity_info is None:
            if activity.activity_id in _DEPRECATED_ACTIVITIES_FOUND_IN_DATASET:
                fcn = _DEPRECATED_ACTIVITIES_FOUND_IN_DATASET[activity.activity_id]
            else:
                LOGGER.warning(f"New deprected activity found {activity.activity_id}.")
                return
        else:
            fcn = activity_info["fullClassName"]
        activity.fqn = fcn
        activity.activity_id = fcn
        activity.is_dynamic = False

    def translate(self, wf: Workflow) -> Workflow:
        wf = copy.deepcopy(wf)
        self.walk_workflow(wf)
        return wf


class WorkflowThoughtsModifier(SyntheticStepsWalker):
    thoughts: list[str]
    verbose: bool

    def __init__(self, thoughts: list[str], verbose: bool = True):
        super().__init__()
        self.thoughts = thoughts[::-1]  # to be popped
        self.verbose = verbose

    @override
    def process_activity(self, activity: Activity) -> None:
        existing_display_name = activity.display_name
        activity.display_name = self.thoughts.pop()
        if activity.fqn == "UiPath.Core.Activities.ManualTrigger":
            activity.display_name = "Manual Trigger"  # it will be overwritten anyway (this helps with diffs)
        if self.verbose and SyntheticStepsWalker.SYNTHETIC_STEP_ACTIVITY_ID == activity.activity_id and activity.display_name != existing_display_name:
            print(f"WARNING! Slight mismatch in synthetic step key={existing_display_name} dname={activity.display_name}")

    def replace(self, wf: Workflow):
        self.walk_workflow(wf)
        assert self.thoughts == [], f"Not all thoughts were used, {self.thoughts}"


class WorkflowThoughtsModifierWithLookup(Walker):
    id_to_thoughts: dict[str, str]

    def __init__(self, id_to_thoughts: dict[str, str]):
        self.id_to_thoughts = id_to_thoughts

    @override
    def process_activity(self, activity: Activity) -> None:
        activity.display_name = self.id_to_thoughts.get(activity.id, activity.display_name)
        if activity.fqn == "UiPath.Core.Activities.ManualTrigger":
            activity.display_name = "Manual Trigger"  # it will be overwritten anyway (this helps with diffs)

    def replace(self, workflow: Workflow) -> None:
        self.walk_workflow(workflow)


class ActivityIdCollectorIncludingSyntheticSteps(SyntheticStepsWalker):
    ids: list[str]

    def __init__(self):
        super().__init__(self)
        self.ids = []

    @override
    def process_activity(self, activity: Activity) -> None:
        self.ids.append(activity.id)

    def get_ids(self, workflow: Workflow) -> list[str]:
        self.ids = []
        self.walk_workflow(workflow)
        return self.ids


class ActivityIdCollector(Walker):
    ids: list[str]

    def __init__(self):
        self.ids = []

    @override
    def process_activity(self, activity: Activity) -> None:
        self.ids.append(activity.id)

    def collect(self, workflow: Workflow) -> list[str]:
        self.ids = []
        self.walk_workflow(workflow)
        return self.ids


class ActivitiesAndTriggersCollector(Walker):
    trigger: Activity | None
    activities: list[Activity]

    def __init__(self):
        self.trigger = None
        self.activities = []

    @override
    def process_activity(self, activity: Activity) -> None:
        self.activities.append(activity)

    def collect(self, workflow: Workflow) -> dict[schema.ActivityType, list[Activity]]:
        self.walk_workflow(workflow)
        if workflow.trigger is not None:
            self.trigger, *self.activities = self.activities
        return {
            "activity": self.activities,
            "trigger": [self.trigger] if self.trigger is not None else [],
        }


class ActivityThoughtsCollector(Walker):
    activities: list[Activity]

    def __init__(self):
        self.trigger = None
        self.activities = []

    @override
    def process_activity(self, activity: Activity) -> None:
        self.activities.append(activity)

    def collect(self, workflow: Workflow) -> dict[schema.ActivityType, list[Activity]]:
        self.walk_workflow(workflow)
        if workflow.trigger is not None:
            self.trigger, *self.activities = self.activities
        return {
            "activity": self.activities,
            "trigger": [self.trigger] if self.trigger is not None else [],
        }


class TestWorkflowParenthoodValidity(Walker):
    def __init__(self):
        self.reset()

    def reset(self):
        self._parent_stack = []
        self.is_valid = True

    @override
    def before_processing_activity_tree(self, activity):
        self._parent_stack.append(activity)

    @override
    def after_processing_activity_tree(self, activity: Activity) -> None:
        self._parent_stack.pop()

    @override
    def process_activity(self, activity: Activity) -> None:
        if not self._parent_stack:
            print(f"Warning! No parent in stack for {activity}")
            self.is_valid = False
        elif activity.parent is not self._parent_stack[-1]:
            print(f"Warning! Found a mismatch {activity.parent} {self._parent_stack[-1]}")
            self.is_valid = False

    @override
    def before_processing_workflow_tree(self, workflow: Workflow) -> None:
        self._parent_stack.append(workflow)

    @override
    def after_processing_workflow_tree(self, workflow: Workflow) -> None:
        self._parent_stack.pop()

    def validate(self, workflow: Workflow) -> bool:
        self.reset()
        self.walk_workflow(workflow)
        return self.is_valid and not self._parent_stack


class WorkflowPruner(Walker):
    def __init__(self):
        self.reset()

    def reset(self):
        self._pruned = False

    def prune_activity(self, activity: Activity) -> list[Activity] | None:
        """Should return a list of activities to prune with. Return None if no pruning is needed."""
        if activity.fqn == "System.Activities.Statements.TryCatch":
            return activity.activities.get("Try", [])
        if activity.fqn == "UiPath.Core.Activities.CommentOut":
            return []

    @override
    def process_sequence(self, sequence: list[Activity], *args):
        """Traverse sequence and prune activities inline

        The pruning process will replace the activities that return a list when
        called with the `prune_activity` method. The activity will be replaced
        with the returned list of activities (childs). This will happen inline.

        Examples:
        >>> self.prune_activity(B)
        [B1, B2]
        >>> sequence = [A, B, C]
        >>> self._traverse_and_prune(sequence)
        >>> sequence
        [A, B1, B2, C]
        """
        offset = 0
        for i, child_activity in enumerate(sequence[:]):
            if (new_activities := self.prune_activity(child_activity)) is not None:
                for activity in new_activities:
                    activity.parent = child_activity.parent
                sequence[offset + i : offset + i + 1] = new_activities
                offset += len(new_activities) - 1
                self._pruned = True

    def before_processing_workflow_tree(self, workflow):
        activity_typedefs, additional_typedefs = FillMissingWorkflowActivityTypedefs(workflow, {}, {}, [constants.SEQUENCE_ACTIVITY_NAME]).fill()
        self.original_variables = CollectWorkflowVariables(workflow, activity_typedefs, additional_typedefs).collect()

    def after_processing_workflow_tree(self, workflow):
        activity_typedefs, additional_typedefs = FillMissingWorkflowActivityTypedefs(workflow, {}, {}, [constants.SEQUENCE_ACTIVITY_NAME]).fill()
        new_variables = CollectWorkflowVariables(workflow, activity_typedefs, additional_typedefs).collect()
        for variable_name in set(new_variables["defined_by_user"]) - set(self.original_variables["defined_by_user"]):
            # print(f"Warning! Variable {variable_name} was pruned.")
            del workflow.variables[variable_name]

    def prune(self, workflow: Workflow):
        """This is the entrypoint for pruning a workflow"""
        self.reset()
        self.walk_workflow(workflow)
        return self._pruned


class SequenceGenerationGenericActivityReplacer(Walker):
    def __init__(self, sequence: list[Activity]):
        self.sequence = sequence
        self.replaced = False

    def replace(self, workflow: Workflow):
        self.replaced = False
        self.walk_workflow(workflow)

    @override
    def process_sequence(self, sequence, *args):
        if self.replaced:
            return
        for i, activity in enumerate(sequence):
            if activity.fqn != constants.SEQUENCE_ACTIVITY_NAME:
                continue
            if activity.display_name != constants.SEQUENCE_GENERATION_INSERTION_PHRASE:
                continue
            sequence[i : i + 1] = self.sequence
            self.replaced = True
            break


class SequenceGenerationCurrentActivityReplacer(Walker):
    def __init__(self, assume_first_empty_sequence: bool = False):
        self.assume_first_empty_sequence = assume_first_empty_sequence

    @override
    def process_activity(self, activity: Activity):
        if activity.fqn == constants.SEQUENCE_ACTIVITY_NAME and not activity.activities and not activity.activity_delegates:
            if activity.is_current_activity:
                activity.display_name = constants.SEQUENCE_GENERATION_INSERTION_PHRASE
            elif self.assume_first_empty_sequence:
                activity.display_name = constants.SEQUENCE_GENERATION_INSERTION_PHRASE

    def replace(self, workflow: Workflow):
        self.walk_workflow(workflow)


class ActivityTypeCollector(Walker):
    typed_activity_instances: dict[str, list[Activity]]

    def __init__(self):
        self.typed_activity_instances = collections.defaultdict(list)

    @override
    def process_activity(self, activity: Activity) -> None:
        self.typed_activity_instances[activity.activity_id].append(activity)

    def get_activity_types(self) -> set[str]:
        return set(self.typed_activity_instances.keys())

    def iterate_activities_once_per_type(self) -> t.Iterable[Activity]:
        for _, activity_list in self.typed_activity_instances.items():
            yield activity_list[0]


class IntegrationServiceActivityReplacer(Walker):
    """
    This walker is responsible for replacing the IS activity types with the virtual activity type
    stored in the embeddings.db for matching the activity type definition returned by the DAP CLI.
    """

    uuid_to_class_name: dict[str, str]

    def __init__(self, uuid_to_class_name):
        self.uuid_to_class_name = uuid_to_class_name

    @override
    def process_activity(self, activity: Activity) -> None:
        if activity.is_dynamic and activity.id in self.uuid_to_class_name:
            activity.fqn = self.uuid_to_class_name[activity.id]


class IntegrationServiceActivityRestorer(Walker):
    """
    This walker is responsible for restoring the IS activity initial type (i.e. ConnectorActivity/ConnectorTriggerActivity)
    and the DAP configuration info that wasn't passed to the model due to token frugality reasons.
    """

    uuid_to_restore_info: dict[str, dict]

    def __init__(self, uuid_to_restore_info):
        self.uuid_to_restore_info = uuid_to_restore_info

    @override
    def process_activity(self, activity: Activity) -> None:
        if activity.id not in self.uuid_to_restore_info:
            return

        actual_config = self.uuid_to_restore_info[activity.id]

        activity.is_dynamic = True
        activity.dap_is_configured = actual_config["activityIsConfigured"]
        activity.fqn = actual_config["actualActivityName"]
        activity.dap_connector_key = actual_config["connectorKey"]
        activity.dap_config = actual_config["configuration"]
        activity.dap_type_id = actual_config["activityTypeId"]
        activity.dynamic_activity_details = actual_config["dynamicActivityDetails"]


class FillMissingWorkflowActivityTypedefs(Walker):
    """
    Some activity type definitions are missing from the workflow, so we supplement them from the embeddings.db typedefs. Modifies typedefs in place.
    Make sure to have the the typedefs.py module .load()-ed before using this walker.
    """

    workflow: Workflow
    activity_typedefs: dict[str, TypeDef]

    def __init__(
        self,
        workflow: Workflow,
        activity_typedefs: dict[str, TypeDef],
        additional_typedefs: dict[str, TypeDef],
        force_activities: list | None = None,
        strict_mode: bool = True,
    ) -> None:
        self.strict_mode = strict_mode
        self.workflow = workflow
        self.activity_typedefs = activity_typedefs
        self.additional_typedefs = additional_typedefs
        self.force_activities = force_activities or []

    def fill(self) -> tuple[dict[str, TypeDef], dict[str, TypeDef]]:
        self.walk(self.workflow)
        return self.activity_typedefs, self.additional_typedefs

    @override
    def process_workflow(self, workflow: Workflow):
        for activity_id in self.force_activities:
            self.add_activity_typedef(activity_id)

    @override
    def process_activity(self, activity: Activity) -> None:
        if activity.activity_id not in self.activity_typedefs:
            self.add_activity_typedef(activity.activity_id)

    def add_activity_typedef(self, activity_id):
        if not typedefs.activity_typedef_exists(activity_id):
            if self.strict_mode:
                raise errors.MissingActivityTypedefError(f"{activity_id} does not exist as global typedef.")
            else:
                print(f"Warning! {activity_id} not found in typedefs.")
                return
        self.activity_typedefs[activity_id] = typedefs.get_activity_typedef(activity_id)
        activity_additional_typedefs = typedefs.get_activity_aditional_typedefs(activity_id)
        for name, typedef in activity_additional_typedefs.items():
            if name not in self.additional_typedefs:
                self.additional_typedefs[name] = typedef


class FillMissingWorkflowOutArguments(Walker):
    """
    Some activities are missing output params so we add them with a simple logic to encourage the model to always fill output parameters.
    Modifies the workflow in place.
    """

    workflow: Workflow
    workflow_variables: dict[str, Variable]
    activity_typedefs: dict[str, TypeDef]
    additional_typedefs: dict[str, TypeDef]

    def __init__(self, workflow: Workflow, activity_typedefs: dict[str, TypeDef], additional_typedefs: dict[str, TypeDef]) -> None:
        self.workflow = workflow
        self.activity_typedefs = activity_typedefs
        self.additional_typedefs = additional_typedefs
        self.workflow_variables = {**self.workflow.arguments, **self.workflow.variables}

    def fill(self):
        self.walk(self.workflow)

    def process_activity(self, activity: Activity) -> None:
        if activity.activity_id in self.activity_typedefs:
            activity_typedef = self.activity_typedefs[activity.activity_id]
        elif typedefs.activity_typedef_exists(activity.activity_id):
            activity_typedef = typedefs.get_activity_typedef(activity.activity_id)
        else:
            return
        activity_params = activity.params
        new_activity_params = {}
        for param_name, param_typedef in activity_typedef["params"].items():
            if param_typedef["category"] == "OutArgument" and param_name not in activity_params:
                variable_name, i = param_typedef["name"], 0
                while variable_name in self.workflow_variables:
                    variable_name.removesuffix(f"{i}")
                    i += 1
                    variable_name += f"{i}"
                if variable_name in self.workflow_variables:
                    LOGGER.error(f"Generated variable name {variable_name} already exists.")
                variable_type = typedefs.get_outargument_type(param_typedef)
                new_activity_params[param_name] = f"[[{variable_name}]]"
                self.workflow.variables[variable_name] = {"name": variable_name, "type": variable_type}
        activity.primitive_properties.update(new_activity_params)
        activity.params.update(new_activity_params)
        activity.params_original_order = tuple(list(activity.params_original_order) + list(new_activity_params.keys()))


class CollectWorkflowVariables(Walker):
    workflow: Workflow
    variables: WorkflowVariables

    def __init__(
        self,
        workflow: Workflow,
        activity_typedefs: dict[str, TypeDef],
        additional_typedefs: dict[str, TypeDef],
        strict_mode: bool = True,
    ) -> None:
        self.strict_mode = strict_mode
        self._visited_current_activity = False
        self.workflow = workflow
        self.activity_typedefs = activity_typedefs
        self.additional_typedefs = additional_typedefs
        self.variables = {
            "in_scope": {},
            "out_of_scope": {},
            "defined_by_arguments": {},
            "defined_by_activity": {},
            "defined_by_activity_delegate": {},
            "defined_by_user": {},
        }

    def collect(self) -> WorkflowVariables:
        self.walk(self.workflow)
        return self.variables

    def process_workflow(self, workflow: Workflow) -> None:
        self.variables["defined_by_arguments"] = {name: {"name": arg["name"], "type": arg["type"]} for name, arg in self.workflow.arguments.items()}
        self.variables["in_scope"].update(self.variables["defined_by_arguments"])

    def process_activity(self, activity: Activity) -> None:
        variable_scoped_collection = self.variables["out_of_scope"] if self._visited_current_activity else self.variables["in_scope"]
        # collect variables defined in ActivityDelegate
        for variables in activity.local_variables.values():
            for variable in variables.values():
                self.variables["defined_by_activity_delegate"][variable["name"]] = variable
                variable_scoped_collection[variable["name"]] = variable
        # collect variables defined in activity OutArguments
        if activity.activity_id not in self.activity_typedefs:
            if self.strict_mode:
                raise errors.MissingActivityTypedefError(activity.activity_id)
            else:
                print(f"Warning! {activity.activity_id} typedef not found.")
        params_typedefs = self.activity_typedefs.get(activity.activity_id, {}).get("params", {})
        for param_key, param_value in activity.primitive_properties.items():
            param_typedef = params_typedefs.get(param_key)
            is_out_argument = param_typedef is not None and param_typedef["category"] == "OutArgument"
            if is_out_argument:
                variable_name = param_value.removeprefix("[[").removesuffix("]]")
                if variable_name in self.workflow.variables:
                    variable = self.workflow.variables[variable_name]
                    self.variables["defined_by_activity"][variable_name] = variable
                    variable_scoped_collection[variable["name"]] = variable
        self._visited_current_activity = self._visited_current_activity or activity.is_current_activity

    def after_processing_workflow_tree(self, workflow: Workflow) -> None:
        # collect variables defined by the user
        for variable in self.workflow.variables.values():
            if variable["name"] not in self.variables["in_scope"] and variable["name"] not in self.variables["out_of_scope"]:
                self.variables["defined_by_user"][variable["name"]] = variable
        self.variables["in_scope"].update(self.variables["defined_by_user"])


class BuildActivityConfigDemoExamples(Walker):
    name: str
    target_framework: TargetFramework
    workflow: Workflow
    activity_typedefs: dict[str, schema.TypeDef]
    additional_typedefs: dict[str, schema.TypeDef]

    step: int
    examples: list[activity_config_schema.ActivityConfigExample]

    def __init__(self, workflow_generation_example: dict, workflow_generation_id: str) -> None:
        self.name = workflow_generation_id
        self.target_framework = workflow_generation_example["target_framework"]
        self.workflow = Workflow(workflow_generation_example["description"], workflow_generation_example["plan"], workflow_generation_example["process"])
        self.activity_typedefs, self.additional_typedefs = typedefs_parser.parse_workflow_conversion_typedefs(workflow_generation_example.get("typedefs", {}))
        self.activity_retriever = ActivitiesRetriever()
        self.step, self.examples = 0, []

    def build(self) -> list[activity_config_schema.ActivityConfigExample]:
        self.walk(self.workflow)
        return self.examples

    def process_workflow(self, workflow: Workflow) -> None:
        FillMissingWorkflowActivityTypedefs(self.workflow, self.activity_typedefs, self.additional_typedefs).fill()
        FillMissingWorkflowOutArguments(self.workflow, self.activity_typedefs, self.additional_typedefs).fill()

    def process_activity(self, activity: Activity) -> None:
        self.step += 1
        activity.is_current_activity = True
        # Get typedefs
        activity_typedef = self.activity_typedefs[activity.activity_id]
        activity_typedef_text = activity_typedef["text"]
        additional_typedefs_text = "\r\n".join([typedef["text"] for typedef in self.additional_typedefs.values()])
        # We temporarily set the default display name on the current activity to simulate most common scenario workflow state.
        current_activity_display_name = activity.display_name
        activity_definition = self.activity_retriever.get(activity.activity_id, self.target_framework) or {}
        default_activity_display_name = activity_definition.get("displayName", "")
        activity.display_name = default_activity_display_name
        example_workflow = self.workflow.to_dict(include_name=True)
        # Collect workflow variables and type definitions that are in scope for the current activity.
        workflow_variables = CollectWorkflowVariables(self.workflow, self.activity_typedefs, self.additional_typedefs).collect()
        workflow_variables_in_scope = workflow_variables["in_scope"]
        example_available_variables = copy.deepcopy(list(workflow_variables_in_scope.values()))
        # Create example configuration
        example_params = params.sanitize(self._get_activity_configurable_params(activity), activity_typedef)
        # Create the example.
        example_id = f"{self.name}/{self.step - 1:02d}___{activity.activity_id}"
        example_input: activity_config_schema.ActivityConfigInput = {
            "target_framework": self.target_framework,
            "user_query": current_activity_display_name,
            "workflow": example_workflow,
            "available_variables": example_available_variables,
            "activity_typedef": activity_typedef_text,
            "additional_typedefs": additional_typedefs_text,
        }
        example_output: activity_config_schema.ActivityConfigResult = {
            "explanation": "...",
            "configuration": example_params,
        }
        example = activity_config_schema.ActivityConfigExample(
            id=example_id,
            demo_id=example_id,
            workflow_id=self.name,
            activity_id=activity.activity_id,
            activity_fqn=activity.fqn,
            input=example_input,
            output=example_output,
        )
        self.examples.append(copy.deepcopy(example))
        # Revert activity modifications and continue.
        activity.display_name = current_activity_display_name
        activity.is_current_activity = False

    def _get_activity_configurable_params(self, activity: Activity) -> dict[str, str]:
        configurable_params = {}
        configurable_params.update(activity.primitive_properties)
        configurable_params.update(activity.simple_list_properties)
        configurable_params.update(activity.complex_list_properties)
        configurable_params.update(activity.simple_dict_properties)
        configurable_params.update(activity.complex_dict_properties)
        for meta_param_key in constants.ACTIVITY_META_PARAMS:
            configurable_params.pop(meta_param_key, None)
        configurable_params_original_order = {}
        for key in activity.params_original_order:
            if key in configurable_params:
                configurable_params_original_order[key] = configurable_params[key]
        return copy.deepcopy(configurable_params_original_order)


class BuildActivityConfigEvalExamples(Walker):
    """Takes a ActivityConfigExample generated by BuildActivityConfigurationDemoExamples and create multiple examples with the params of the current activity sampled from the provided values."""

    examples: list[activity_config_schema.ActivityConfigExample]

    def __init__(self, activity_config_demo_example: activity_config_schema.ActivityConfigExample):
        self.activity_config_demo_example = activity_config_demo_example
        self.workflow = Workflow("", "", activity_config_demo_example["input"]["workflow"])
        self.activity_typedef = typedefs_parser.parse_typedef(activity_config_demo_example["input"]["activity_typedef"])
        self.examples = []

    def process_activity(self, activity: Activity) -> None:
        if not activity.is_current_activity:
            return
        configurable_params = self._get_activity_configurable_params(activity)
        configurable_param_keys = tuple(configurable_params.keys())
        include_sets = [tuple()]
        # include in exponentially more parameters.
        for i in range(1, len(configurable_param_keys)):
            end = 2**i
            if end >= len(configurable_param_keys):
                break  # we have included all the parameters
            include_sets.append(configurable_param_keys[:end])
        # for each ablation set, create a new example with the ablated parameters removed.
        for i, include_set in enumerate(include_sets):
            workflow_ablation = self.workflow.clone()
            current_activity_ablation = GetCurrentActivity().get(workflow_ablation)
            for key in configurable_params.keys():
                if key not in include_set:
                    del current_activity_ablation.params[key]
            current_activity_ablation.handle_params()
            a, b = self.activity_config_demo_example["id"].split("___")
            example_id = f"{a}___{i:02d}___{b}"
            example = copy.deepcopy(self.activity_config_demo_example)
            example["id"] = example_id
            example["demo_id"] = self.activity_config_demo_example["id"]
            example["input"]["workflow"] = copy.deepcopy(workflow_ablation.to_dict(include_name=True))
            self.examples.append(example)

    def build(self) -> list[activity_config_schema.ActivityConfigExample]:
        self.walk(self.workflow)
        return self.examples

    def _get_activity_configurable_params(self, activity: Activity) -> dict[str, str]:
        configurable_params = {}
        configurable_params.update(activity.primitive_properties)
        configurable_params.update(activity.simple_list_properties)
        configurable_params.update(activity.complex_list_properties)
        configurable_params.update(activity.simple_dict_properties)
        configurable_params.update(activity.complex_dict_properties)
        for meta_param_key in constants.ACTIVITY_META_PARAMS:
            configurable_params.pop(meta_param_key, None)
        configurable_params_original_order = {}
        for key in activity.params_original_order:
            if key in configurable_params:
                configurable_params_original_order[key] = configurable_params[key]
        return copy.deepcopy(configurable_params_original_order)


class GetCurrentActivity(Walker):
    current_activity: Activity | None

    def __init__(self):
        self.reset()

    def reset(self):
        self.current_activity = None

    def get(self, workflow: Workflow) -> Activity:
        self.reset()
        self.walk_workflow(workflow)
        if self.current_activity is None:
            raise errors.MissingCurrentActivityError()
        return self.current_activity

    @override
    def process_activity(self, activity: Activity) -> None:
        if activity.is_current_activity:
            if self.current_activity is not None:
                raise errors.MultipleCurrentActivitiesError()
            self.current_activity = activity


class ActivityIdFinder(Walker):
    """Walker that collects activities based on their IDs."""

    def __init__(self, ids: list[str]):
        """Initialize with the list of activity IDs to find."""
        self.ids_to_find = set(ids)  # Using a set for faster lookups
        self.found_activities = {}  # Will map IDs to Activity instances

    @override
    def process_activity(self, activity: Activity) -> None:
        """Check if the current activity's ID is in our list, and collect it if so."""
        if activity.id in self.ids_to_find:
            self.found_activities[activity.id] = activity

    def find(self, workflow: Workflow) -> dict[str, Activity]:
        """Walk the workflow and return the matching activities."""
        self.found_activities = {}  # Reset in case this is reused
        self.walk_workflow(workflow)
        return self.found_activities


class SanitizeWorkflowActivites(Walker):
    def __init__(self, allowed_param_types: set[ParamTypeCategory], allowed_param_values: set[ParamValueCategory]) -> None:
        self.allowed_param_types = allowed_param_types
        self.allowed_param_values = allowed_param_values

    def process_activity(self, activity: Activity) -> None:
        full_activity_name, _ = extract_generic_pattern(activity.fqn)
        activity.fqn = full_activity_name.split(".")[-1]
        for key, value in tuple(activity.params.keys()):
            param_value_category = params.get_param_value_category(value)
            if not self.allowed_param_values or param_value_category not in self.allowed_param_values:
                del activity.params[key]
        activity.handle_params()

    def sanitize(self, workflow: Workflow) -> None:
        self.walk_workflow(workflow)


def erase_after_current_activity(workflow: Workflow, remove_current_activity_children: bool = False):
    """
    This function traverses the workflow and removes all activities and triggers that come after the current activity, hierarchically (including ascendants).
    It will also remove sibling sequences on the ancestral line of the current activity that come after the sequence containing the current activity.
    Raises:
        errors.MissingCurrentActivityError: If no current activity is found in the workflow.
        errors.MultipleCurrentActivitiesError: If multiple current activities are found in the workflow.
        TypeError: If objects throughout workflow hierarchy are not of the expected type.
    """
    activities = ActivitiesAndTriggersCollector().collect(workflow)
    activities = activities["activity"] + activities["trigger"]
    current_activities = [activity for activity in activities if activity.is_current_activity]
    if len(current_activities) == 0:
        raise errors.MissingCurrentActivityError()
    if len(current_activities) > 1:
        raise errors.MultipleCurrentActivitiesError()
    current_activity = current_activities[0]
    current_parent = current_activity.parent
    # Remove activities not on the same "stem" as the current activity.
    while current_parent:
        if not isinstance(current_activity, Activity):
            raise TypeError(f"Current activity is not an Activity but a {type(current_activity)}")
        if isinstance(current_parent, Workflow):
            if current_parent.trigger is current_activity:
                current_parent.activities[:] = []
            elif current_activity in (sequence := current_parent.activities):
                # we need to do this elif check because the trigger might contain the current activity inside (trigger with child activities) - SRE-325538
                sequence[:] = sequence[: sequence.index(current_activity) + 1]
            else:
                LOGGER.error("Could not find current activity. It could potentially be inside the trigger")
                raise errors.UnprocessableEntityError("Malformed trigger")
                # current_parent.activities[:] = []
        else:
            for sequence_container in (current_parent.activities, current_parent.activity_delegates):
                iteration_is_past_current_activity = False
                for key, sequence in sequence_container.items():
                    if current_activity in sequence:
                        sequence_container[key] = sequence[: sequence.index(current_activity) + 1]
                        iteration_is_past_current_activity = True
                    elif iteration_is_past_current_activity:
                        sequence_container[key][:] = []  # should we maybe delete the key instead of setting it to []?
        # Remove any children of the current activity.
        if remove_current_activity_children:
            for param_key in tuple(current_activity.activity_delegates.keys()) + tuple(current_activity.activities.keys()):
                del current_activity.params[param_key]
            current_activity.handle_params()
        current_activity, current_parent = current_parent, current_parent.parent


class BuildActivityConfigurationContext(Walker):
    target_framework: TargetFramework
    current_workflow: Workflow
    current_variables: list[Variable]
    current_activity_typedef_text: str
    current_additional_typedefs_text: str

    params_to_keep_on_current_activity: set[str] | None

    current_activity: Activity | None
    current_activity_typedef: TypeDef | None
    current_additional_typedefs: dict[str, TypeDef] | None

    def __init__(
        self,
        input: activity_config_schema.ActivityConfigInput,
        params_to_keep_on_current_activity: set[str] | None,
        max_workflow_length: int = 100,
        max_activity_property_length: int = 10000,
    ) -> None:
        self.target_framework = input["target_framework"]
        self.user_query = input["user_query"]
        self.current_workflow = self._parse_workflow(input["workflow"])
        self.current_activity_typedef_text = input["activity_typedef"]
        self.current_additional_typedefs_text = input["additional_typedefs"]
        self.current_variables = input["available_variables"]

        self.params_to_keep_on_current_activity = params_to_keep_on_current_activity
        self.max_workflow_length = max_workflow_length

        self.current_activity = None
        self.current_activity_typedef = None
        self.current_additional_typedefs = None

        self.workflow_pruner = CollapseWorkflowSubsequencesPruner()
        self.activity_properties_truncator = ActivityPropertiesTruncator(max_activity_property_length)

    def _parse_workflow(self, workflow_dict: WorkflowDict) -> Workflow:
        # attempt to mitigate the special case when all activities might reside inside the trigger
        if (trigger := workflow_dict.get("trigger")) is not None and trigger["activity"] == constants.SEQUENCE_ACTIVITY_NAME:
            activities = trigger.get("params", {}).get("Activities", [])
            if activities and all("trigger" not in activity.get("activity").lower() for activity in activities[1:]):
                workflow_dict["trigger"] = activities[0]  # actual trigger
                workflow_dict["workflow"] = activities[1:] + workflow_dict.get("workflow", [])
        return Workflow("", "", workflow_dict)

    def _prune_workflow_tree(self) -> None:
        workflow_length = len(ActivityIdCollector().collect(self.current_workflow))
        if workflow_length > self.max_workflow_length:
            for _ in self.workflow_pruner.iteratively_prune_workflow(self.current_workflow):
                workflow_length = len(ActivityIdCollector().collect(self.current_workflow))
                if workflow_length <= self.max_workflow_length:
                    return

    def _truncate_activity_properties(self) -> dict[str, dict[str, str]]:
        return self.activity_properties_truncator.truncate(self.current_workflow)

    def build(self) -> activity_config_schema.ActivityConfigContext:
        self.walk(self.current_workflow)
        if self.current_activity is None:
            raise errors.MissingCurrentActivityError()
        if self.current_activity_typedef is None or self.current_additional_typedefs is None:
            raise errors.MissingActivityTypedefError(self.current_activity.activity_id)
        self._prune_workflow_tree()
        original_param_values = self._truncate_activity_properties()
        current_workflow_dict: WorkflowDict = self.current_workflow.to_dict(include_packages=False)
        current_activity_dict: ActivityDict = self.current_activity.to_dict(include_params_when_empty=True, include_ids=True)
        current_activity_types_dict: ActivityDict = {
            "thought": current_activity_dict["thought"],
            "activity": current_activity_dict["activity"],
            "params": {k: v["type"] for k, v in self.current_activity_typedef["params"].items()},
        }
        return {
            "activity_id": self.current_activity.activity_id,
            "target_framework": self.target_framework,
            "user_query": self.user_query,
            "workflow": current_workflow_dict,
            "activity": current_activity_dict,
            "activity_types": current_activity_types_dict,
            "activity_typedef": self.current_activity_typedef,
            "additional_typedefs": self.current_additional_typedefs,
            "original_param_values": original_param_values,
        }

    def process_workflow(self, workflow: Workflow) -> None:
        assert self.current_workflow is not None
        for variable in self.current_variables:
            variable["type"] = self._get_variable_short_type(variable["type"])
        self.current_workflow.arguments = {}
        self.current_workflow.variables = {variable["name"]: variable for variable in self.current_variables}

    def process_activity(self, activity: Activity) -> None:
        if not activity.is_current_activity:
            return
        if self.current_activity is not None:
            raise errors.MultipleCurrentActivitiesError()
        self.current_activity = activity
        # clear workflow after current activity
        erase_after_current_activity(self.current_workflow)
        if self.params_to_keep_on_current_activity is not None:
            for key in tuple(self.current_activity.params.keys()):
                if key not in self.params_to_keep_on_current_activity:
                    del self.current_activity.params[key]
            self.current_activity.handle_params()
        # parse activity type definition
        self.current_activity_typedef = typedefs_parser.parse_typedef(self.current_activity_typedef_text)
        # TODO: Choose and/or improve either
        # self._collect_additional_typedefs_2()
        self._collect_additional_typedefs()

    def _collect_additional_typedefs(self) -> None:
        """This is the original implementation that filters additional typedefs aggresively."""
        assert self.current_activity_typedef is not None, "Current activity typedef is not set."
        # Parse type definitions
        all_additional_typedefs = typedefs_parser.parse_typedefs(self.current_additional_typedefs_text)
        current_additional_typedefs = {}
        # get type definitions of current activity variables
        for variable in itertools.chain(self.current_workflow.variables.values(), self.current_workflow.arguments.values()):
            components = typedefs_parser.get_type_components(variable["type"])
            for _type in components:
                if _type in all_additional_typedefs:
                    current_additional_typedefs[_type] = all_additional_typedefs[_type]
                elif typedefs.additional_typedef_exists(_type):
                    current_additional_typedefs[_type] = typedefs.get_additional_typedef(_type)
        # get type definitions of current activity
        for param in self.current_activity_typedef["params"].values():
            for _type in param["components"]:
                if _type in all_additional_typedefs:
                    current_additional_typedefs[_type] = all_additional_typedefs[_type]
                elif typedefs.additional_typedef_exists(_type):
                    current_additional_typedefs[_type] = typedefs.get_additional_typedef(_type)
        self.current_additional_typedefs = current_additional_typedefs

    def _collect_additional_typedefs_2(self) -> None:
        """This implementation does not do any filtering on the provided additional typedefs. Initial experiments show a degradation in performance."""
        assert self.current_activity_typedef is not None, "Current activity typedef is not set."
        # Parse type definitions
        current_additional_typedefs = typedefs_parser.parse_typedefs(self.current_additional_typedefs_text)
        # TODO: Remove this once cleared with Studio team.
        # Add type definitions for variable if they are not already present
        # This should not happen in practice, but we are adding this as a safety measure.
        for variable in itertools.chain(self.current_workflow.variables.values(), self.current_workflow.arguments.values()):
            components = typedefs_parser.get_type_components(variable["type"])
            for _type in components:
                if _type not in current_additional_typedefs and typedefs.additional_typedef_exists(_type):
                    current_additional_typedefs[_type] = typedefs.get_additional_typedef(_type)
        # Get type definitions of current activity
        for param in self.current_activity_typedef["params"].values():
            for _type in param["components"]:
                if _type not in self.current_additional_typedefs_text and typedefs.additional_typedef_exists(_type):
                    current_additional_typedefs[_type] = typedefs.get_additional_typedef(_type)
        self.current_additional_typedefs = current_additional_typedefs

    def _get_variable_short_type(self, variable_type: str) -> str:
        components = variable_type.split("<")
        short_components = []
        for component in components:
            short_components.append(component.rstrip(">").split(".").pop())
        variable_type = "<".join(short_components) + ">" * (len(short_components) - 1)
        return variable_type


class WorkflowSequenceExtractor:
    """This class is responsible for extracting a sequence from a workflow and the corresponding cutting points."""

    counter: int  # ephemeral state variable

    def __init__(self):
        self.placeholder_activity_lmyaml: ActivityDict = {
            "thought": constants.SEQUENCE_GENERATION_INSERTION_PHRASE,
            "activity": constants.SEQUENCE_ACTIVITY_NAME,
            "params": {},
        }
        self.counter = 0

    def _iterate_activities(self, activity: Activity, parent: list[Activity]) -> t.Iterator[tuple[Activity, list[Activity]]]:
        """This should go in the visitor"""
        yield activity, parent
        for sequence in activity.activities.values():
            for child_activity in sequence:
                yield from self._iterate_activities(child_activity, sequence)
        for sequence in activity.activity_delegates.values():
            for child_activity in sequence:
                yield from self._iterate_activities(child_activity, sequence)

    def iterate_activites(self, workflow: Workflow) -> t.Iterator[tuple[Activity, list[Activity]]]:
        # do not iterate on the trigger for this extraction
        for activity in workflow.activities:
            yield from self._iterate_activities(activity, workflow.activities)

    def _extract_sequence_from_parent_index_range(self, start: int, end: int, parent: list[Activity], activity_for_metadata: Activity):
        sequence = copy.deepcopy(parent[start : end + 1])
        placeholder_activity = Activity(
            self.placeholder_activity_lmyaml, activity_for_metadata.workflow_arguments, activity_for_metadata.workflow_variables, activity_for_metadata.parent
        )
        placeholder_activity.is_current_activity = True  # this is necessary to obtain variables up to this point
        parent[start : end + 1] = [placeholder_activity]
        return sequence

    def _extract_sequence_including_activity(self, activity: Activity, parent: list[Activity]):
        """This modifies the parent"""
        index = [i for i, a in enumerate(parent) if a is activity][0]
        start = random.randint(0, index)
        end = random.randint(index, len(parent) - 1)
        start_activity, end_activity = parent[start], parent[end]

        sequence = self._extract_sequence_from_parent_index_range(start, end, parent, activity)
        return sequence, start_activity, end_activity

    def _extract_sequence_from_activity_range(self, start_activity: Activity, end_activity: Activity, parent: list[Activity]):
        """This modifies the parent"""
        start = [i for i, a in enumerate(parent) if a is start_activity][0]
        end = [i for i, a in enumerate(parent) if a is end_activity][0]
        sequence = self._extract_sequence_from_parent_index_range(start, end, parent, start_activity)
        return sequence

    def create_sequence(
        self,
        workflow: Workflow,
        cut_indices: tuple[int, int] | None = None,
        activity_typedefs: dict[str, TypeDef] | None = None,
        additional_typedefs: dict[str, TypeDef] | None = None,
    ):
        """Generate a random cut or cut a specific range from the workflow"""
        activity_typedefs = activity_typedefs or {}
        additional_typedefs = additional_typedefs or {}

        cut_workflow = copy.deepcopy(workflow)
        activities_and_parents = list(self.iterate_activites(cut_workflow))
        if cut_indices is None:
            activity_to_remove, activity_parent = random.choice(activities_and_parents)
            sequence, start_activity, end_activity = self._extract_sequence_including_activity(activity_to_remove, activity_parent)
            start_cut = [i for i, (a, p) in enumerate(activities_and_parents) if a is start_activity][0]
            end_cut = [i for i, (a, p) in enumerate(activities_and_parents) if a is end_activity][0]
        else:
            start_cut, end_cut = cut_indices
            start_activity, parent = activities_and_parents[start_cut]
            end_activity, parent2 = activities_and_parents[end_cut]
            assert parent is parent2, f"Parent mismatch: {parent} != {parent2}"
            sequence = self._extract_sequence_from_activity_range(start_activity, end_activity, parent)
        sequence_workflow = copy.deepcopy(workflow)
        sequence_workflow.trigger = None
        sequence_workflow.activities = sequence

        # get user defined variables from the entire workflow (visibility)
        workflow_visible_variables = CollectWorkflowVariables(workflow, activity_typedefs, additional_typedefs, strict_mode=False).collect()
        user_defined_global_variables = {k: v for k, v in workflow_visible_variables["defined_by_user"].items() if k not in cut_workflow.arguments}

        dummy_cut_workflow_for_variables = copy.deepcopy(cut_workflow)
        erase_after_current_activity(dummy_cut_workflow_for_variables)
        _vars = CollectWorkflowVariables(dummy_cut_workflow_for_variables, activity_typedefs, additional_typedefs, strict_mode=False).collect()
        sequence_visible_variables = {
            k: v for k, v in _vars["in_scope"].items() if k not in _vars["defined_by_user"] and k not in _vars["defined_by_arguments"]
        }
        sequence_visible_variables.update(user_defined_global_variables)
        if re.search(r"ExtractorResult: ['\"]?\[\[\w+\]\]['\"]?", dummy_cut_workflow_for_variables.lmyaml()):
            sequence_visible_variables.pop("ExtractorResult")  # TODO: remove this once fixed
        cut_workflow.variables = copy.deepcopy(sequence_visible_variables)  # to avoid reference issues when dumping
        sequence_workflow.variables = copy.deepcopy(cut_workflow.variables)  # this should not be used for the dataset, but setting for any eventuality
        return cut_workflow, sequence_workflow, start_cut, end_cut

    def _create_cutting_points_tree_from_sequence(self, activity_sequence: list[Activity]):
        """Helper function to create the cutting points tree from a sequence. Mutual recurrence with activites."""
        sequence_cutting_points = []
        inner_cutting_points = []
        for activity in activity_sequence:
            sequence_cutting_points.append(self.counter)
            inner_cutting_points.extend(self._create_cutting_points_tree_from_activity(activity))
        return {"cutting_points": sequence_cutting_points, "inner_cutting_points": inner_cutting_points}

    def _create_cutting_points_tree_from_activity(self, activity: Activity):
        """Helper function to create the cutting points tree from an activity. Mutual recurrence with sequences."""
        activity_cutting_points = []
        self.counter += 1
        for sequence in activity.activities.values():
            activity_cutting_points.append(self._create_cutting_points_tree_from_sequence(sequence))
        for sequence in activity.activity_delegates.values():
            activity_cutting_points.append(self._create_cutting_points_tree_from_sequence(sequence))
        return activity_cutting_points

    def generate_cutting_points(self, workflow: Workflow):
        """Generates the comprehensive list of cutting points for the workflow"""
        self.counter = 0
        cutting_points_tree = self._create_cutting_points_tree_from_sequence(workflow.activities)

        queue = collections.deque([cutting_points_tree])
        all_cutting_points = []
        while queue:
            node = queue.popleft()
            cutting_points = node["cutting_points"]
            if not cutting_points:  # leaf node
                continue
            for i in range(len(cutting_points)):
                for j in range(i, len(cutting_points))[::-1]:
                    all_cutting_points.append((cutting_points[i], cutting_points[j]))
            for child_node in node["inner_cutting_points"]:
                queue.append(child_node)
        return all_cutting_points


class CollapseWorkflowSubsequencesPruner:
    """Trims a workflow by iteratively collapsing subsequences."""

    def __init__(self, collapse_along_deepest_activity: bool = False):
        self.collapse_along_deepest_activity = collapse_along_deepest_activity

    def _create_placeholder_subsequence(self, thought: str) -> schema.ActivityDict:
        return {"thought": f"<collapsed>\n{thought}\n</collapsed>", "activity": "Placeholder.Collapsed.Subsequence", "params": {}}

    def _find_current_activity(self, activity: Activity) -> Activity | None:
        if activity.is_current_activity or activity.display_name == constants.SEQUENCE_GENERATION_INSERTION_PHRASE:
            return activity
        for sequence in itertools.chain(activity.activities.values(), activity.activity_delegates.values()):
            for child_activity in sequence:
                if result := self._find_current_activity(child_activity):
                    return result

    def find_current_activity(self, workflow: Workflow) -> Activity | None:
        for activity in workflow.activities:
            if result := self._find_current_activity(activity):
                return result

    def find_deepest_activity(self, workflow: Workflow) -> Activity | None:
        activities_by_level = []
        current_level_activities = [workflow.trigger] if workflow.trigger is not None else []
        current_level_activities += workflow.activities

        while current_level_activities:
            activities_by_level.append(current_level_activities[:])
            next_level_activities = []
            for activity in current_level_activities:
                for sequence in itertools.chain(activity.activities.values(), activity.activity_delegates.values()):
                    next_level_activities.extend(sequence)
            current_level_activities = next_level_activities
        if not activities_by_level:
            return
        last_level = activities_by_level[-1]
        if not last_level:
            return  # this check is redundant for the current implementation, but I'm leaving it here for future changes
        return last_level[len(last_level) // 2]  # we could choose the deepest activity in a more sophisticated way

    def _construct_subplan_steps(self, activities: list[Activity], indent=0):
        # we could incorporate the planbuilder here, but it requires some changes (no numbering, custom thought, etc.)
        subplan_steps = []
        for activity in activities:
            subplan_steps.append("  " * indent + f"{activity.fqn} ({activity.display_name})")
            for sequence_container in (activity.activities, activity.activity_delegates):
                has_many_branches = len(activity.activities) > 1
                for key, sequence in sequence_container.items():
                    if has_many_branches and key != "Then":
                        subplan_steps.append("  " * (indent + 1) + f"{key}")
                    subplan_steps.extend(self._construct_subplan_steps(sequence, indent + 1 + int(has_many_branches)))
        return subplan_steps

    def construct_subplan(self, activities: list[Activity]):
        return "\n".join(self._construct_subplan_steps(activities))

    def _collapse_subsequence(self, sequence: list[Activity]):
        if not sequence:
            raise ValueError("Cannot collapse an empty sequence")
        pivot_activity = sequence[0]
        _args, _vars, _parent = pivot_activity.workflow_arguments, pivot_activity.workflow_variables, pivot_activity.parent
        # we could aggregate these and do some async summarization calls, but for the moment a subplan representation is created
        return [Activity(self._create_placeholder_subsequence(self.construct_subplan(sequence)), _args, _vars, _parent)]

    def iteratively_prune_workflow(self, workflow: Workflow) -> t.Iterator[str]:
        """This method modifies the workflow and returns an iterator that would advance the pruning. The yielded value is a str identifier.

        The collapsing process is iterative, allowing for selective trimming until a desirable size is reached. This is achieved by prioritizing
        the other sequences before trimming the left and right subsequences in relation to the stem, level by level starting from the root,
        on the path to the current activity.
        """
        if self.collapse_along_deepest_activity:
            current_activity = self.find_deepest_activity(workflow)
            if not current_activity:
                return
        else:
            current_activity = self.find_current_activity(workflow)
            if not current_activity:
                raise errors.MissingCurrentActivityError()
            if not current_activity.is_current_activity and current_activity.display_name != constants.SEQUENCE_GENERATION_INSERTION_PHRASE:
                raise errors.MissingCurrentActivityError()

        # create direct child parent ancestry for current activity
        act = current_activity
        ancestry: list[tuple[Activity | Workflow, Activity | Workflow]] = []  # (child, parent)
        while act.parent is not None:
            ancestry.append((act, act.parent))
            act = act.parent

        # Create the necessary pointers for the hierarchical traversal
        level_datapoint = []
        for depth, (direct_child, parent) in enumerate(ancestry[::-1]):
            # split by current and other sequences
            activity_sequences: list[list[Activity]] = (
                [parent.activities] if isinstance(parent, Workflow) else list(parent.activity_delegates.values()) + list(parent.activities.values())
            )
            current_sequence: list[Activity] = []
            other_sequences: list[list[Activity]] = []
            for sequence in activity_sequences:
                if not sequence:
                    LOGGER.warning(f"Empty sequence in {parent}.")
                    continue
                for activity in sequence:
                    if activity is direct_child:
                        current_sequence = sequence
                        break
                else:
                    other_sequences.append(sequence)
            if not current_sequence:
                raise errors.ExistingWorkflowInvalidError("Could not find the current sequence in the workflow.")

            # split current sequence in left and right sequences
            left: list[Activity] = []
            right: list[Activity] = []
            _current_container: list[Activity] = left
            for activity in current_sequence:
                if activity is direct_child:
                    _current_container = right
                    continue
                _current_container.append(activity)

            level_datapoint.append((depth, left, direct_child, right, current_sequence, other_sequences))

        # This trims by creating a stem along the path to the current activity, with each side having one activity followed by collapsed subsequences.
        collapsed_left_sequences_by_depth = {}
        for depth, left, direct_child, right, current_sequence, other_sequences in level_datapoint:
            # prune other sequences
            # ideas:
            # - we could prioritize the pruning of such "other sequences" on all the levels before moving towards the left-right pruning
            if other_sequences:
                for sequence in other_sequences:
                    sequence[:] = self._collapse_subsequence(sequence)
                yield f"depth_{depth}_other_sequences"

            # prune current sequence
            # ideas:
            # - we could prune on all the depth levels at the same time, from the longest left, rigth sequences

            # geometric trimming variant
            collapsed_right = right
            for i in range(10):  # upper bound assumption that we don't have more than 2**10 activities on right
                n_to_collapse = 2**i
                if not 0 < n_to_collapse < len(right) - 2:
                    break  # we want to leave at least two activities on the right
                # collapsed_right = right[:]
                # collapsed_right[-n_to_collapse:] = self._collapse_subsequence(right[-n_to_collapse:])
                collapsed_right = right[:-n_to_collapse] + self._collapse_subsequence(right[-n_to_collapse:])
                current_sequence[:] = left + [direct_child] + collapsed_right
                yield f"depth_{depth}_right_{n_to_collapse}/{len(right)}"

            collapsed_left = left
            for i in range(10):  # upper bound assumption that we don't have more than 2**10 activities on left
                n_to_collapse = 2**i
                if not 0 < n_to_collapse < len(left) - 2:
                    break  # we want to leave at least two activities on the left
                collapsed_left = self._collapse_subsequence(left[:n_to_collapse]) + left[n_to_collapse:]
                current_sequence[:] = collapsed_left + [direct_child] + collapsed_right
                yield f"depth_{depth}_left_{n_to_collapse}/{len(left)}"

            # simpler variant - collapses everything except for one activity for left and right
            if len(right) > 1:
                collapsed_right = right[:1] + self._collapse_subsequence(right[1:])
                current_sequence[:] = collapsed_left + [direct_child] + collapsed_right
                yield f"depth_{depth}_right_leave_one_out"
            if len(left) > 1:
                collapsed_left = self._collapse_subsequence(left[:-1]) + left[-1:]
                current_sequence[:] = collapsed_left + [direct_child] + collapsed_right
                yield f"depth_{depth}_left_leave_one_out"

            collapsed_left_sequences_by_depth[depth] = collapsed_left

        # This trims by leaving only the activities along the path to the current activity
        # - assumes other sequences are already collapsed from the previous step
        # - assumes that the descendants of the current depth level are also trimmed in the previous iteration fashion
        collapsed_right_sequences_by_depth = {}
        for depth, _left, direct_child, right, current_sequence, _other_sequences in level_datapoint:
            # collapses everything from the right of the stem
            collapsed_left = collapsed_left_sequences_by_depth[depth]
            collapsed_right = right
            if len(right):
                collapsed_right = self._collapse_subsequence(right)
                current_sequence[:] = collapsed_left + [direct_child] + collapsed_right
                yield f"depth_{depth}_right_final"
            collapsed_right_sequences_by_depth[depth] = collapsed_right

        for depth, left, direct_child, _right, current_sequence, _other_sequences in level_datapoint:
            collapsed_right = collapsed_right_sequences_by_depth[depth]
            if len(left):
                collapsed_left = self._collapse_subsequence(left)
                current_sequence[:] = collapsed_left + [direct_child] + collapsed_right
                yield f"depth_{depth}_left_final"


class PlanBuilderWithCategories(PlanBuilder):
    def __init__(self, target_framework, force_trigger_if_missing=True):
        super().__init__(force_trigger_if_missing=force_trigger_if_missing)
        self.target_framework = target_framework
        self.activities_retriever = ActivitiesRetriever()

    @override
    def process_activity(self, activity: Activity) -> None:
        display_name = formatting_utils.escape_quotes(activity.display_name.strip())
        activity_info = self.activities_retriever.get(activity.activity_id, target_framework=self.target_framework)
        category = "None"
        if activity_info is None:
            category = "None"
        else:
            category = activity_info["mapped_category"]

        if (
            category is not None
            and category not in ["", "None", "UiPathOrchestrator", "Workflow"]
            and not category.startswith("System")
            and not category.startswith("Any application")
            and not category.startswith("None")
        ):
            display_name += f" - using {category}"
        self.add_plan_step(display_name)


class ActivityPropertiesTruncator(Walker):
    """
    This walker is responsible for truncating the value of string properties of an activity if they exceed a certain length. Returns a a map with the original
    values of the truncated properties which can be used in the `ActivityPropertiesRestorer` to restore the original values.
    """

    property_max_length: int
    original_param_values: dict[str, dict[str, str]]

    def __init__(self, property_max_length: int = 10000):
        self.property_max_length = property_max_length
        self.reset()

    def reset(self):
        self.original_param_values = {}

    def process_activity(self, activity: Activity) -> None:
        for param_name, param_value in activity.primitive_properties.items():
            if isinstance(param_value, str) and len(param_value) > self.property_max_length:
                truncated_value = param_value[: self.property_max_length] + "... (truncated value)"
                if param_value.endswith("]]"):
                    truncated_value += "]]"
                if activity.id not in self.original_param_values:
                    self.original_param_values[activity.id] = {}
                self.original_param_values[activity.id][param_name] = param_value
                activity.primitive_properties[param_name] = truncated_value

    def truncate(self, wf: Workflow) -> dict[str, dict[str, str]]:
        self.reset()
        self.walk_workflow(wf)
        return self.original_param_values


class ActivityPropertiesRestorer(Walker):
    """
    This walker is responsible for restoring the original values of properties that were truncated using the `ActivityPropertiesTruncator`. Consumes the
    original values map obtained from the `ActivityPropertiesTruncator`.
    """

    original_param_values: dict[str, dict[str, str]]

    def process_activity(self, activity: Activity) -> None:
        if activity.id in self.original_param_values:
            for param_name, param_value in self.original_param_values[activity.id].items():
                activity.primitive_properties[param_name] = param_value

    def restore(self, wf: Workflow, original_param_values: dict[str, dict[str, str]]) -> None:
        self.original_param_values = original_param_values
        self.walk_workflow(wf)
