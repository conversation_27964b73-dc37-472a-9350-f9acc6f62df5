import asyncio
from typing import Any

from services.studio._text_to_workflow.api_workflow.services.activity_schema_search_service import ActivitySchemaSearchService
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load
from services.studio._text_to_workflow.workflow_generation.workflow_generation_schema import UndocumentedHttpRequest

LOGGER = AppInsightsLogger()


async def generate_http_requests_schema(undocumented_http_requests: list[UndocumentedHttpRequest] | None) -> list[dict[str, Any]]:
    search_tasks = []
    http_requests_schema = []
    web_search_service = ActivitySchemaSearchService()
    if undocumented_http_requests is None or len(undocumented_http_requests) == 0:
        return []
    for undocumented_http_request in undocumented_http_requests:
        search_query = (
            f"Activity name: {undocumented_http_request.name}\n"
            f"Activity description: {undocumented_http_request.description}\n"
            f"Provider: {undocumented_http_request.provider}"
        )
        search_tasks.append(web_search_service.web_search(search_query))

    search_results = await asyncio.gather(*search_tasks, return_exceptions=True)

    for search_result, undocumented_http_request in zip(search_results, undocumented_http_requests, strict=False):
        if isinstance(search_result, BaseException):
            LOGGER.error(f"Failed to retrieve JSON schema for activity {undocumented_http_request}: {search_result}")
            continue
        try:
            api_search_result = yaml_load(search_result["result"])["discovered_apis"][0]
            provider = yaml_load(search_result["result"])["provider"]

            http_requests_schema.append(
                {
                    "query": undocumented_http_request.name,
                    "provider": provider,
                    "method": api_search_result["request_type"],
                    "endpoint": api_search_result["api_base_url"],
                    "jsonSchema": api_search_result["swagger_schema_string"],
                }
            )
        except Exception as e:
            LOGGER.error(f"Failed to generate HTTP request schema for activity {undocumented_http_request}: {e}")
            continue
    return http_requests_schema
