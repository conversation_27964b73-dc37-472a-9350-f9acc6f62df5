import re

from langchain.output_parsers import YamlOutputParser
from langchain.output_parsers.fix import OutputFixingParser
from langchain.prompts import PromptTemplate
from langchain_core.language_models import BaseChatModel
from pydantic import BaseModel

from services.studio._text_to_workflow.utils.yaml_utils import enforce_single_quotes_on_yaml_values

# The default pattern will not capture any text after a ` inside our YAML markup, so we use this instead
YAML_CAPTURE_PATTERN: re.Pattern = re.compile(r"^```(?:ya?ml)?(?P<yaml>[\s\S]*.*?)```", re.MULTILINE | re.DOTALL)
# keys that are not escapable, we will not enforce single quotes on them
NON_ESCAPABLE_KEYS: set[str] = {"limit"}


class APIWFYamlOutputParser(YamlOutputParser):
    """YamlOutputParser subclass that enforces a specific schema for YAML API Workflows"""

    def parse(self, text: str) -> dict:
        """Apply enforce_single_quotes_on_yaml_values before parsing the YAML."""
        fixed_text = enforce_single_quotes_on_yaml_values(text, allow_double_quoted_values=True, non_escapeable_keys=NON_ESCAPABLE_KEYS)
        # frequent exceptions can occur here, we ignore them as they are frequently fixed by the output fixing parser
        return super().parse(fixed_text)  # @IgnoreException

    async def aparse(self, text: str) -> dict:
        """Apply enforce_single_quotes_on_yaml_values before asynchronously parsing the YAML."""
        fixed_text = enforce_single_quotes_on_yaml_values(text, allow_double_quoted_values=True, non_escapeable_keys=NON_ESCAPABLE_KEYS)
        # frequent exceptions can occur here, we ignore them as they are frequently fixed by the output fixing parser
        return await super().aparse(fixed_text)  # @IgnoreException


# we created this as a separate function for ease of testing
def build_api_wf_pydantic_parser_with_fix(llm: BaseChatModel, pydantic_object: type[BaseModel], prompt: PromptTemplate) -> OutputFixingParser:
    return OutputFixingParser.from_llm(parser=APIWFYamlOutputParser(pydantic_object=pydantic_object, pattern=YAML_CAPTURE_PATTERN), llm=llm, prompt=prompt)
