import asyncio
import copy
import dataclasses
import html
import itertools
import json
import pathlib
import tempfile
import time
import traceback
import warnings
from contextlib import contextmanager
from fnmatch import fnmatch
from itertools import zip_longest
from pathlib import Path
from typing import Callable, Optional

import Levenshtein
import numpy as np
from dotenv import load_dotenv
from tabulate import tabulate

from services.studio._text_to_workflow.common import activity_retriever, constants
from services.studio._text_to_workflow.common.schema import ActivitiesGenerationMode, Connection, SubsetName, TargetFramework, Variable
from services.studio._text_to_workflow.common.typedefs_parser import parse_workflow_conversion_typedefs
from services.studio._text_to_workflow.common.walkers import (
    ActivitiesAnd<PERSON>riggersCollector,
    PlanBuilder,
    SequenceGenerationCurrentActivityReplacer,
    SynopsisBuilder,
)
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils import dotnet_workflow_converter, paths, workflow_utils
from services.studio._text_to_workflow.utils.errors import MissingCurrentActivityError
from services.studio._text_to_workflow.utils.formatting_utils import unescape_quotes
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.workflow_utils import (
    cleanup_rpa_wf_workflow_for_compare,
    load_workflow_instance,
    normalize_activity_dep,
    renumber_plan_steps,
    use_normalized_activity_dep,
    use_typeid_in_activities,
)
from services.studio._text_to_workflow.utils.yaml_utils import yaml_dump, yaml_load
from services.studio._text_to_workflow.workflow_generation import workflow_generation_dataset, workflow_generation_task
from services.studio._text_to_workflow.workflow_generation._evaluate_retrieval import (
    get_activity_full_id,
    get_sample_activities,
    get_sample_trigger,
    iou,
    recall,
)
from services.studio._text_to_workflow.workflow_generation._tree_edit_distance import core_wf_workflow_edit_score, planning_edit_score, same_activity_in_mapping
from services.studio._text_to_workflow.workflow_generation.workflow_generation_helpers import get_identifier

# Filter out FutureWarning from transformers
warnings.filterwarnings("ignore", category=FutureWarning)


def remap_dataset(dataset, force_sequence_generation_entire_sequences=False):
    ret = {}
    for fname, sample in dataset.items():
        if force_sequence_generation_entire_sequences and not fname.name.startswith("00__"):
            continue
        sample["filename"] = fname
        pivot = sample.get("description_sequence", sample["description"])
        if force_sequence_generation_entire_sequences:
            pivot = sample["description"]
        ret[pivot] = sample
    return ret


class ActivityTreeAccuracyCalculator:
    def __init__(self, activity_identifier_key: str = "activity"):
        self.activity_identifier_key = activity_identifier_key

    def _get_activities(self, workflow):
        activities = []
        if isinstance(workflow, dict):
            if self.activity_identifier_key in workflow:
                activities.append(workflow[self.activity_identifier_key])
            for key in workflow:
                activities.extend(self._get_activities(workflow[key]))
        if isinstance(workflow, list):
            for i in workflow:
                activities.extend(self._get_activities(i))
        return activities

    def _count_activities(self, workflow):
        return len(self._get_activities(workflow))

    def tree_accuracy(self, ngt, npr):
        good, total = 0, 0
        if isinstance(ngt, str):
            return 0, self._count_activities(npr)
        if isinstance(ngt, dict):
            if isinstance(npr, dict):
                if self.activity_identifier_key in ngt:
                    if isinstance(npr, dict) and self.activity_identifier_key in npr:
                        if ngt[self.activity_identifier_key] == npr[self.activity_identifier_key]:
                            # print("same activity", ngt["activity"])
                            good += 1
                        # else:
                        # print("distinct activity", ngt["activity"], npr["activity"])
                        total += 1
                    else:
                        # print("no activity compatible with", ngt["activity"])
                        good, total = good, total + max(1, self._count_activities(npr))

                for key in ngt:
                    if key in npr:
                        # print("subkey", key)
                        g, t = self.tree_accuracy(ngt[key], npr[key])
                        good, total = good + g, total + t
                    else:
                        good, total = good, total + self._count_activities(ngt[key])
            else:
                total += self._count_activities(ngt)
        if isinstance(ngt, list):
            if isinstance(npr, list):
                for igt, ipr in zip_longest(ngt, npr, fillvalue=None):
                    # print("sublist")
                    g, t = self.tree_accuracy(igt, ipr)
                    good, total = good + g, total + t
            else:
                total += sum(self._count_activities(i) for i in ngt)
        # from pprint import pprint

        # print("=== returning", good, total)
        # pprint(ngt)
        # pprint(npr)
        return good, total


async def validate_workflow_format(model_output: dict, oneline_errors: bool) -> dict:
    with tempfile.NamedTemporaryFile(suffix=".yaml", mode="w", delete=False) as input_tempfile:
        json.dump(model_output, input_tempfile)
        input_filepath = input_tempfile.name
    with tempfile.NamedTemporaryFile(delete=False, suffix=".json") as output_tempfile:
        output_filepath = output_tempfile.name
    response = await dotnet_workflow_converter.convert_yaml2workflow(input_filepath, output_filepath)
    errors = [error.split("\n")[0] for error in response["Errors"]] if oneline_errors else response["Errors"]
    has_errors = bool(errors)
    partially_deserializable = response["Success"] and has_errors
    fully_deserializable = response["Success"] and not has_errors
    return {
        "partially_deserializable": partially_deserializable,
        "fully_deserializable": fully_deserializable,
        "invalid": not partially_deserializable and not fully_deserializable,
        "output": response["Output"],
        "errors": errors,
    }


def load_dataset_from_evaluation_index_file(
    evaluation_index_file: pathlib.Path | None,
    evaluation_key: str | None,
    mode: ActivitiesGenerationMode,
    target_framework: TargetFramework,
) -> dict[pathlib.Path, dict]:
    """Loads paths from the evaluation index file and collects all the yamls into a dataset dictionary."""
    if evaluation_index_file is None:
        evaluation_index_file = paths.get_workflow_generation_dataset_path(target_framework) / "subsets.yaml"
    if evaluation_index_file.suffix == ".yaml":  # expect a list with paths inside
        evaluation_paths = yaml_load(evaluation_index_file)
        assert evaluation_key is not None and evaluation_key in evaluation_paths, f"Key {evaluation_key} not found in {evaluation_index_file}"
        if evaluation_key is not None:
            evaluation_paths = evaluation_paths[evaluation_key]
    else:  # expect paths on each line
        evaluation_paths = [p.strip() for p in evaluation_index_file.read_text().splitlines() if p.strip()]
    evaluation_paths = [pathlib.Path(p) for p in evaluation_paths]
    # remove .yaml suffixes if present
    evaluation_paths = [p.with_suffix("") if p.suffix == ".yaml" else p for p in evaluation_paths]

    # allow for relative paths as well
    if mode == "workflow":
        evaluation_paths = [p.with_name(p.name + ".yaml") for p in evaluation_paths]
        dataset_root_test = paths.get_workflow_generation_dataset_path(target_framework, "test")
        dataset_root_train = paths.get_workflow_generation_dataset_path(target_framework, "train")
    else:
        dataset_root_test = paths.get_sequence_generation_dataset_path(target_framework, "test")
        dataset_root_train = paths.get_sequence_generation_dataset_path(target_framework, "train")
    valid_paths: list[pathlib.Path] = []
    for path in evaluation_paths:
        if path.exists():
            valid_paths.append(path)
        elif (tentative_path := (path.with_name(path.name + ".yaml"))).exists():
            valid_paths.append(tentative_path)
        elif (tentative_path := (dataset_root_test / path)).exists():
            valid_paths.append(tentative_path)
        elif (tentative_path := (dataset_root_train / path)).exists():
            valid_paths.append(tentative_path)
        else:
            print(f"Warning! Could not find path for evaluation: {path}")

    # expand paths for sequence generation / other use cases with directories
    expanded_paths = []
    for path in valid_paths:
        if path.is_dir():  # verify if path is directory
            expanded_paths.extend(path.glob("**/*.yaml"))
        else:
            expanded_paths.append(path)
    return {path: yaml_load(path) for path in expanded_paths}


prefix = """
<!DOCTYPE html>
<html>
<head>
<meta name="viewport" content="width=device-width, initial-scale=1">
<style>
/* General Page Styling */
body {
  font-family: Arial, sans-serif;
  line-height: 1.6;
  background-color: #f9f9f9;
  color: #333;
  margin: 0;
  padding: 0;
}

/* Headings */
h1 {
  padding: 15px;
  text-align: center;
  margin: 0;
}

.heading {
  background-color: #e0e0e0;
  color: #333;
  width: 100%;
  text-align: left;
  font-size: 20px;
  position: -webkit-sticky; /* For Safari */
  position: sticky;
  top: 0; /* Stick to the top */
  z-index: 1000; /* Ensure it stays above content */
}

/* Collapsible Buttons */
.collapsible {
  background-color: #e0f7fa;
  cursor: pointer;
  width: 100%;
  border: none;
  text-align: left;
  outline: none;
  font-size: 15px;
  padding: 3px;
  margin: 0;
  transition: background-color 0.3s ease;
  position: -webkit-sticky; /* For Safari */
  position: sticky;
  top: 20px; /* Stick to the top */
  z-index: 100; /* Ensure it stays above content */
}

.active, .collapsible:hover {
  background-color: #555;
}

.collapsible:after {
  content: '\25BC'; /* Unicode character for a down arrow */
  font-size: 12px;
  color: white;
  float: right;
  margin-left: 5px;
}

.collapsible.active:after {
  content: '\25B2'; /* Unicode character for an up arrow */
}

/* Collapsible Content */
.content {
  display: none;
  overflow: hidden;
  background-color: #f1f1f1;
  padding: 0 18px;
  border-left: 3px solid #4CAF50;
  margin: 0;
}

/* Preformatted Text */
pre {
  background-color: #2e2e2e;
  color: #f1f1f1;
  padding: 15px;
  border-radius: 5px;
  overflow-x: auto;
  font-size: 14px;
}

/* Tables */
/* Table Container for Scrollability */
.table-container {
  max-width: 100%;
  overflow-x: auto; /* Horizontal scroll for wide tables */
  margin: 20px 0;
  border: 1px solid #ccc;
  border-radius: 5px;
  box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

/* Table Styling */
table {
  width: 100%;
  border-collapse: collapse;
  font-size: 14px;
  background-color: #fff;
  table-layout: auto; /* Let the browser adjust column widths */
  border: 1px solid #ddd;
}

th, td {
  padding: 10px;
  text-align: left;
  border: 1px solid #ddd;
  vertical-align: middle; /* Align content properly */
  word-wrap: break-word; /* Wrap long content */
}

th {
  background-color: #4CAF50;
  color: white;
  position: sticky; /* Sticky header for better navigation */
  top: 0;
  z-index: 10;
}

tbody tr:nth-child(odd) {
  background-color: #f9f9f9;
}

tbody tr:nth-child(even) {
  background-color: #f1f1f1;
}

tbody tr {
  color: black;
}

tbody tr:hover {
  background-color: #e8f5e9; /* Subtle highlight on hover */
  cursor: pointer;
}

/* Optional: Fixed Column Widths */
table th:nth-child(1),
table td:nth-child(1) {
  width: 20%; /* Adjust based on your content */
}

table th:nth-child(2),
table td:nth-child(2) {
  width: 30%;
}

/* Optional: Scrollbar Styling */
.table-container::-webkit-scrollbar {
  height: 8px;
}

.table-container::-webkit-scrollbar-thumb {
  background: #888;
  border-radius: 10px;
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: #555;
}
</style>
</head>
<body>
<h1>Evaluation Details</h1>

"""

suffix = """
<script>
var coll = document.getElementsByClassName("collapsible");
var i;
for (i = 0; i < coll.length; i++) {
  coll[i].addEventListener("click", function() {
    this.classList.toggle("active");
    var content = this.nextElementSibling;
    if (content.style.display === "block") {
      content.style.display = "none";
      var rect = this.getBoundingClientRect();
      if (rect.top < 0 || rect.bottom > window.innerHeight) {
        this.scrollIntoView({ block: "start" });
        window.scrollBy(0, -20);
      }
    } else {
      content.style.display = "block";
    }
  });
}
</script>
</body>
</html>
"""

div_template_prefix = """\
<div>
<button type="button" class="collapsible">{title}</button>
<pre style="display:none">
"""

div_template_suffix = """\
</pre>
</div>
"""


@contextmanager
def collapsible_block(title: str, print_log_queue: Optional[list[callable]] = None):
    if print_log_queue:
        print_log_queue.append(lambda: print(div_template_prefix.format(title=title)))
    else:
        print(div_template_prefix.format(title=title))
    try:
        yield
    finally:
        if print_log_queue:
            print_log_queue.append(lambda: print(div_template_suffix))
        else:
            print(div_template_suffix)


div_template = """\
<div>
<button type="button" class="collapsible">{title}</button>
<pre style="display:none">
{content}
</pre>
</div>"""

heading_template = """
<h2 class="heading">{content}</h2>
"""


class ResultJsonEncoder(json.JSONEncoder):
    def default(self, obj):  # type: ignore
        if isinstance(obj, np.integer):
            return int(obj)
        elif isinstance(obj, np.floating):
            return float(obj)
        elif isinstance(obj, np.ndarray):
            return obj.tolist()
        elif isinstance(obj, TokenUsage):
            return obj.to_json()
        elif isinstance(obj, Path):
            return str(obj)
        else:
            return super(ResultJsonEncoder, self).default(obj)


async def run(
    query: str | None,
    target_framework: TargetFramework,
    subsets: list[SubsetName],
    connections: list[Connection],
    retrieval_model_name: str | None = None,
    generation_model_name: str | None = None,
    mode: ActivitiesGenerationMode = "workflow",
    localization: str | None = None,
    log_file: str | None = None,
    **kwargs,
):
    # other more specific params
    limit: int | None = kwargs.pop("limit", None)
    seed: int | None = kwargs.pop("seed", None)
    use_ideal_plan: bool = kwargs.pop("use_ideal_plan", False)
    use_retrieval: bool = kwargs.get("run_retrieval", True)
    use_generation: bool = kwargs.pop("run_generation", True)
    force_sequence_generation_entire_sequences: bool = kwargs.pop("force_sequence_generation_entire_sequences", False)
    evaluation_index: pathlib.Path | None = kwargs.pop("evaluation_index", None)
    evaluation_key: str | None = kwargs.pop("evaluation_key", None)
    evaluation_cadence: tuple[int, int] | None = kwargs.pop("evaluation_cadence", None)
    evaluation_glob: str | None = kwargs.pop("evaluation_glob", None)

    predefined_current_workflow: str | None = kwargs.pop("predefined_current_workflow", None)
    predefined_variables: list[Variable] | None = kwargs.pop("predefined_variables", None)
    predefined_additional_type_definitions: str | None = kwargs.pop("predefined_additional_type_definitions", None)
    predefined_connections: list[Connection] | None = kwargs.pop("predefined_connections", None)
    connections = predefined_connections or connections

    scores_no_extras, scores, plan_scores, plan_tree_edit_scores, accuracies, demo_scores = [], [], [], [], [], []
    ted_scores, pled_scores, peed_scores, eled_scores, eeed_scores = [], [], [], [], []
    levenshtein_params_score, exact_params_score, levenshtein_expressions_score, exact_expressions_score = [], [], [], []
    fully_valid, partially_valid, invalid, errors = 0, 0, 0, {}
    trigger_recalls, activities_recalls = [], []
    trigger_ious, activities_ious = [], []
    retrieval_strict_recalls = []
    elapsed_times = []
    durations = []

    print(prefix)
    with collapsible_block("Loading prerequisites"):
        load_dotenv(override=True)
        text2workflow = workflow_generation_task.WorkflowGenerationTask(config_name="prompt.yaml").load()
        reranking_model = ModelManager().get_reranking_model()
        reranking_model._initialize()
        cross_encoder_model = reranking_model.model

    dataset: dict[pathlib.Path, dict] = {}
    if evaluation_index is not None or evaluation_key is not None:
        dataset = load_dataset_from_evaluation_index_file(evaluation_index, evaluation_key, mode, target_framework)
    else:
        if mode == "workflow":
            for subset in subsets:
                dataset |= workflow_generation_dataset.load_workflow_generation_subset(target_framework, subset)
        else:
            for subset in subsets:
                dataset |= workflow_generation_dataset.load_sequence_generation_subset(target_framework, subset)
    if evaluation_glob is not None:
        dataset = {k: v for k, v in dataset.items() if fnmatch(k.as_posix(), evaluation_glob)}
    if evaluation_cadence is not None:
        pick_nth, every_nth = evaluation_cadence
        dataset = {k: v for i, (k, v) in enumerate(dataset.items()) if i % every_nth == pick_nth - 1}
    dataset = remap_dataset(dataset, force_sequence_generation_entire_sequences)
    texts = list(dataset.keys()) if query is None else [query]

    if limit:
        texts = texts[:limit]

    usage = {
        "prompt_tokens": 0,
        "completion_tokens": 0,
    }

    ended_with_exceptions = []
    all_logs = []

    batch_size = 8
    for batch_start in range(0, len(texts), batch_size):
        batch_end = min(batch_start + batch_size, len(texts))
        batch_texts = texts[batch_start:batch_end]

        tasks = [
            evaluate_sample(
                query,
                target_framework,
                connections,
                retrieval_model_name,
                generation_model_name,
                mode,
                localization,
                kwargs,
                seed,
                use_ideal_plan,
                use_retrieval,
                use_generation,
                predefined_current_workflow,
                predefined_variables,
                predefined_additional_type_definitions,
                scores_no_extras,
                scores,
                plan_scores,
                plan_tree_edit_scores,
                accuracies,
                demo_scores,
                ted_scores,
                pled_scores,
                peed_scores,
                eled_scores,
                eeed_scores,
                levenshtein_params_score,
                exact_params_score,
                levenshtein_expressions_score,
                exact_expressions_score,
                fully_valid,
                partially_valid,
                invalid,
                errors,
                trigger_recalls,
                activities_recalls,
                trigger_ious,
                activities_ious,
                retrieval_strict_recalls,
                elapsed_times,
                durations,
                text2workflow,
                cross_encoder_model,
                dataset,
                texts,
                usage,
                ended_with_exceptions,
                all_logs,
                i + batch_start,
                text,
            )
            for i, text in enumerate(batch_texts)
        ]

        results: list[list[Callable]] = await asyncio.gather(*tasks, return_exceptions=True)  # type: ignore

        for print_log_queue_result in results:
            if isinstance(print_log_queue_result, Exception):
                print("Exception in batch:", print_log_queue_result)
            else:
                for print_log in print_log_queue_result:
                    print_log()

    if log_file:
        with open(log_file, "w", encoding="utf-8") as f:
            json.dump(all_logs, f, cls=ResultJsonEncoder)

    print(heading_template.format(content=html.escape("Evaluation summary")))
    print("<pre>")
    print("FULLY VALID COUNT:", fully_valid)
    print("PARTIALLY VALID COUNT:", partially_valid)
    print("INVALID COUNT:", invalid)
    print("ERRORS:")
    sorted_errors = dict(sorted(errors.items(), key=lambda item: item[1], reverse=True))
    num_errors = 0
    for error in sorted_errors:
        print(error, ":", errors[error])
        num_errors += errors[error]
    print("TOTAL ERRORS:", num_errors)
    print("TOTAL UNIQUE ERRORS:", len(sorted_errors))
    print("</pre>")
    print("<pre>")
    # print averages for each key in durations
    if durations:
        for k in durations[0].keys():
            durations_k = [d[k] for d in durations]
            avg = sum(durations_k) / len(durations_k)
            print(f"AVERAGE DURATION OF STEP {k}: ", avg)
            locals()[f"duration_{k}"] = durations_k  # for the table below

    print("</pre>")

    print("<table>")
    for ss in (
        "plan_tree_edit_scores",
        "trigger_recalls",
        "activities_recalls",
        "retrieval_strict_recalls",
        "levenshtein_params_score",
        "ted_scores",
        "pled_scores",
        "duration_planning",
        "duration_generation",
        "elapsed_times",
    ):
        print(f"<tr><td>{ss}</td>", end="")
        if ss not in locals():
            continue
        s = locals()[ss]
        for i in s:
            print(f"<td>{i}</td>", end="")
        print("</tr>")
    print("</table>")
    print("<pre>")
    for ss in (
        "plan_scores",
        "plan_tree_edit_scores",
        "trigger_recalls",
        "trigger_ious",
        "activities_recalls",
        "activities_ious",
        "retrieval_strict_recalls",
        "scores",
        "scores_no_extras",
        "accuracies",
        "levenshtein_params_score",
        "exact_params_score",
        "levenshtein_expressions_score",
        "exact_expressions_score",
        "ted_scores",
        "pled_scores",
        "peed_scores",
        "eled_scores",
        "eeed_scores",
        "elapsed_times",
    ):
        if ss not in locals():
            continue
        s = locals()[ss]
        if len(s) > 0:
            print("average", ss, sum(s) / len(s), len(s))
    print("average prompt tokens", usage["prompt_tokens"] / len(texts))
    print("average completion tokens", usage["completion_tokens"] / len(texts))
    print("</pre>")
    if ended_with_exceptions:
        print("<pre>")
        print("Exceptions:")
        for message in ended_with_exceptions:
            print(message)
        print("</pre>")
    print(suffix)

    return ted_scores, elapsed_times


async def evaluate_sample(
    query,
    target_framework,
    connections,
    retrieval_model_name,
    generation_model_name,
    mode,
    localization,
    kwargs,
    seed,
    use_ideal_plan,
    use_retrieval,
    use_generation,
    predefined_current_workflow,
    predefined_variables,
    predefined_additional_type_definitions,
    scores_no_extras,
    scores,
    plan_scores,
    plan_tree_edit_scores,
    accuracies,
    demo_scores,
    ted_scores,
    pled_scores,
    peed_scores,
    eled_scores,
    eeed_scores,
    levenshtein_params_score,
    exact_params_score,
    levenshtein_expressions_score,
    exact_expressions_score,
    fully_valid,
    partially_valid,
    invalid,
    errors,
    trigger_recalls,
    activities_recalls,
    trigger_ious,
    activities_ious,
    retrieval_strict_recalls,
    elapsed_times,
    durations,
    text2workflow,
    cross_encoder_model,
    dataset,
    texts,
    usage,
    ended_with_exceptions,
    all_logs,
    i,
    text,
):
    current_log = {}

    print_log_queue: list[Callable] = []
    print_log_queue.append(lambda: print("***"))
    header = html.escape(f"{i + 1} / {len(texts)} {text}")
    print_log_queue.append(lambda: print(heading_template.format(content=header)))
    start_time = time.time()

    identifier: str = ""
    existing_workflow_object: Workflow | None = None
    existing_plan: str = ""
    variables: list[Variable] = []
    additional_type_definitions: str = ""
    if text in dataset:
        datapoint = dataset[text]
        identifier = get_identifier(datapoint["filename"])
        if mode == "sequence":
            existing_workflow_object = Workflow("", "", datapoint.get("process_existing", {}))
            # existing_workflow = yaml_dump(existing_workflow_object, default_style="'").strip()
            existing_plan = datapoint.get("plan_existing", "")
            variables = datapoint["process"].get("arguments", []) + datapoint["process"].get("variables", [])
            additional_type_definitions = ""  # TODO: Include additional type definitions from variables
    else:
        identifier = ""
        if mode == "sequence":  # in this case, we have a predefined query
            # parts of the following code must match the preprocessing done in "generate_sequence" method
            existing_workflow_object = load_workflow_instance(query, predefined_current_workflow or "")
            SequenceGenerationCurrentActivityReplacer().replace(existing_workflow_object)
            existing_plan = PlanBuilder().build(existing_workflow_object)
            variables = predefined_variables or []
            additional_type_definitions = predefined_additional_type_definitions or ""

    existing_workflow: str = ""
    ignored_namespaces: set[str] = set()
    if mode == "sequence":  # set up prerequisites
        assert existing_workflow_object is not None
        existing_workflow = existing_workflow_object.lmyaml(include_packages=False, include_arguments=False, include_variables=False)
        if constants.SEQUENCE_GENERATION_INSERTION_PHRASE not in existing_plan:
            raise MissingCurrentActivityError()
        ignored_namespaces = text2workflow._get_ignored_namespaces(existing_workflow_object, target_framework)

    try:
        if use_ideal_plan and text in dataset:
            if mode == "sequence":
                kwargs["use_plan"] = dataset[text]["plan_sequence"]
            else:
                kwargs["use_plan"] = dataset[text]["plan"]
            print_log_queue.append(lambda: print("using ideal plan"))

        model_options = {
            "retrieval": {"model_name": retrieval_model_name},
            "generation": {"model_name": generation_model_name},
        }
        if seed is not None:
            model_options["retrieval"]["seed"] = seed
            model_options["generation"]["seed"] = seed
        with collapsible_block("Running text2workflow - debug", print_log_queue):
            print("<pre>")
            result = await text2workflow.run(
                query=text,
                variables=variables,
                additional_type_definitions=additional_type_definitions,
                connections=connections,
                target_framework=target_framework,
                objects=[],
                model_options=model_options,
                localization=localization,
                mode=mode,
                existing_plan=existing_plan,
                existing_workflow=existing_workflow,
                ignored_namespaces=ignored_namespaces,
                ignored_identifiers={identifier},
                stitch_workflow_on_sequence_generation=mode == "sequence",
                eval_mode=True,
                **kwargs,
            )
            print("</pre>")
    except Exception as e:
        ended_with_exceptions.append(f"{i + 1}/{len(texts)} {text} {e}")
        print_log_queue.append(lambda e=e: print("Exception!", e))

        import sys

        traceback.print_exc(file=sys.stdout)
        result = {
            "plan": "",
            "plan_whole": "",
            "steps": [],
            "workflow_result": {
                "is_response_valid_yaml": False,
                "workflow_valid": "",
                "workflow_raw": "",
                "used_trigger": "",
                "used_activities": [],
                "used_jit_types": [],
                "used_connectors": [],
                "used_packages": [],
                "used_variables": [],
                "used_local_variables": [],
                "used_namespaces": [],
            },
            "sequence_result": {
                "is_response_valid_yaml": False,
                "workflow_valid": "",
                "workflow_raw": "",
                "used_trigger": "",
                "used_activities": [],
                "used_jit_types": [],
                "used_connectors": [],
                "used_packages": [],
                "used_variables": [],
                "used_local_variables": [],
                "used_namespaces": [],
            },
            "retrieved_activities": [],
            "retrieved_triggers": [],
            "retrieved_activities_after_pruning": [],
            "retrieved_triggers_after_pruning": [],
            "retrieved_packages": [],
            "retrieval_prompt": "",
            "retrieval_usage": TokenUsage(),
            "generation_prompt": "",
            "generation_usage": TokenUsage(),
            "demonstrations": {},
            "duration": {},
        }
    elapsed_time = time.time() - start_time
    if result["workflow_result"]["is_response_valid_yaml"]:
        output = {
            "result": result["workflow_result"]["workflow_valid"],
            "jitCommands": result["workflow_result"]["used_jit_types"],
            "connectors": result["workflow_result"]["used_connectors"],
            "usage": {
                "retrieval": dataclasses.asdict(result["retrieval_usage"]),
                "generation": dataclasses.asdict(result["generation_usage"]),
            },
        }
        validation = await validate_workflow_format(output, oneline_errors=True)
        partially_valid += validation["partially_deserializable"]
        fully_valid += validation["fully_deserializable"]
        invalid += validation["invalid"]
        durations.append(result["duration"])
        for error in validation["errors"]:
            errors[error] = errors.get(error, 0) + 1
    else:
        invalid += 1
        validation = {
            "partially_deserializable": False,
            "fully_deserializable": False,
            "invalid": True,
            "output": "",
            "errors": ["Invalid YAML"],
        }

    activity_retriever_obj = activity_retriever.ActivitiesRetriever()
    gt_triggers, gt_activities = [], []
    if text in dataset:
        gt_workflow_object = Workflow("", "", dataset[text]["process"])
        triggers_and_activities = ActivitiesAndTriggersCollector().collect(gt_workflow_object)
        gt_triggers = [activity_retriever_obj.get(act.activity_id, target_framework, "trigger") for act in triggers_and_activities["trigger"]]
        gt_activities = [activity_retriever_obj.get(act.activity_id, target_framework, "activity") for act in triggers_and_activities["activity"]]
        gt_triggers = [act for act in gt_triggers if act is not None]
        gt_activities = [act for act in gt_activities if act is not None]

    retrieved_triggers = [t["fullActivityId"] for t in result["retrieved_triggers"]]
    retrieved_activities = [a["fullActivityId"] for a in result["retrieved_activities"]]

    def activity_fcn_with_typeid(activity):
        if activity["activityTypeId"]:
            return f"{activity['fullClassName']} @{activity['activityTypeId']}"
        return activity["fullClassName"]

    retrieved_triggers_str: list[str] = []
    if text in dataset:
        retrieved_triggers_str = ["Ground truth:"] + ["  " + activity_fcn_with_typeid(act) for act in gt_triggers] + ["", "Retrieved:"]
    retrieved_triggers_str += [f"{t['className']:40} - {activity_fcn_with_typeid(t)}" for t in result["retrieved_triggers"]]
    retrieved_triggers_str = "\n".join(retrieved_triggers_str)

    retrieved_activities_str: list[str] = []
    if text in dataset:
        retrieved_activities_str = ["Ground truth:"]
        retrieved_activities_str += [f"  {act['className']:40} - {activity_fcn_with_typeid(act)}" for act in gt_activities]
        retrieved_activities_str += [""]
    retrieved_activities_str += ["Retrieved:"]
    retrieved_activities_str += [f"{a['className']:40} - {activity_fcn_with_typeid(a)}" for a in result["retrieved_activities"]]
    retrieved_activities_str = "\n".join(retrieved_activities_str)

    usage["prompt_tokens"] += result["retrieval_usage"].prompt_tokens + result["generation_usage"].prompt_tokens
    usage["completion_tokens"] += result["retrieval_usage"].completion_tokens + result["generation_usage"].completion_tokens
    demonstrations_chained = list(itertools.chain(*result["demonstrations"].values()))
    demonstrations_representation = [
        {
            "score": d["similarity"],
            "embeddings": d.get("embedding_score", 0),
            "rerank": d.get("rerank_score", 0),
            "name": d["name"],
        }
        for d in demonstrations_chained
    ]

    print_arguments = {
        "target_framework": target_framework,
        "mode": mode,
        "use_ideal_plan": use_ideal_plan,
        "use_retrieval": use_retrieval,
        "use_generation": use_generation,
        "scores_no_extras": scores_no_extras,
        "scores": scores,
        "plan_scores": plan_scores,
        "plan_tree_edit_scores": plan_tree_edit_scores,
        "accuracies": accuracies,
        "demo_scores": demo_scores,
        "ted_scores": ted_scores,
        "pled_scores": pled_scores,
        "peed_scores": peed_scores,
        "eled_scores": eled_scores,
        "eeed_scores": eeed_scores,
        "levenshtein_params_score": levenshtein_params_score,
        "exact_params_score": exact_params_score,
        "levenshtein_expressions_score": levenshtein_expressions_score,
        "exact_expressions_score": exact_expressions_score,
        "trigger_recalls": trigger_recalls,
        "activities_recalls": activities_recalls,
        "trigger_ious": trigger_ious,
        "activities_ious": activities_ious,
        "retrieval_strict_recalls": retrieval_strict_recalls,
        "text2workflow": text2workflow,
        "cross_encoder_model": cross_encoder_model,
        "dataset": dataset,
        "text": text,
        "current_log": current_log,
        "existing_plan": existing_plan,
        "result": result,
        "elapsed_time": elapsed_time,
        "validation": validation,
        "retrieved_triggers": retrieved_triggers,
        "retrieved_activities": retrieved_activities,
        "retrieved_triggers_str": retrieved_triggers_str,
        "retrieved_activities_str": retrieved_activities_str,
        "demonstrations_chained": demonstrations_chained,
        "demonstrations_representation": demonstrations_representation,
    }

    print_log_queue.append(lambda: print_results(**print_arguments))
    print_log_queue.append(lambda: print(f"Ending evaluation for {i + 1} / {len(texts)}"))
    elapsed_times.append(elapsed_time)

    all_logs.append(current_log)
    return print_log_queue


# TODO: The eval needs to be refactored in a modularized way - we should not have to pass all these arguments
def print_results(
    target_framework,
    mode,
    use_ideal_plan,
    use_retrieval,
    use_generation,
    scores_no_extras,
    scores,
    plan_scores,
    plan_tree_edit_scores,
    accuracies,
    demo_scores,
    ted_scores,
    pled_scores,
    peed_scores,
    eled_scores,
    eeed_scores,
    levenshtein_params_score,
    exact_params_score,
    levenshtein_expressions_score,
    exact_expressions_score,
    trigger_recalls,
    activities_recalls,
    trigger_ious,
    activities_ious,
    retrieval_strict_recalls,
    text2workflow,
    cross_encoder_model,
    dataset,
    text,
    current_log,
    existing_plan,
    result,
    elapsed_time,
    validation,
    retrieved_triggers,
    retrieved_activities,
    retrieved_triggers_str,
    retrieved_activities_str,
    demonstrations_chained,
    demonstrations_representation,
):
    print(div_template.format(title="demonstrations", content=tabulate(demonstrations_representation, headers="keys", tablefmt="html")))
    current_demo_scores = [d["similarity"] for d in demonstrations_chained]
    demo_score = sum(current_demo_scores) / max(1, len(current_demo_scores))
    demo_scores.append(demo_score)
    print("average demo score", demo_score)

    print(div_template.format(title="retrieval_prompt", content=html.escape(result["retrieval_prompt"])))
    print(div_template.format(title="retrieval_usage", content=html.escape(str(result["retrieval_usage"]))))
    print(div_template.format(title="plan", content=html.escape(result["plan"])))
    if mode == "sequence" and existing_plan:
        print(div_template.format(title="plan_existing", content=html.escape(existing_plan)))
        print(div_template.format(title="plan_whole", content=html.escape(result["plan_whole"])))

    if use_ideal_plan and result["steps"]:
        activities_per_step = {
            unescape_quotes(workflow_utils.strip_step(step["text"])): {
                "retrieved_activity_ids": [a["fullActivityId"] for a in step["activities"] + step["triggers"]]
            }
            for step in result["steps"]
        }
        if "Else" in activities_per_step:
            del activities_per_step["Else"]
        for idx, (node, _, _) in enumerate(workflow_utils.iterate_activities(dataset[text]["process"])):
            step = activities_per_step.get(node["thought"], None)
            if step is None:
                raise ValueError(
                    f"There was an activity tree parsing error. Expected activity {node['thought']} not found in step {idx} and test prompt {text}."
                )

            step["expected_activity_id"] = get_activity_full_id(node)
            step["expected_activity_class_name"] = text2workflow.activities_retriever.get(step["expected_activity_id"], target_framework)["fullClassName"]  # type: ignore # noqa

        for _, (id, activity) in enumerate(activities_per_step.items()):
            expected_activity_id = activity["expected_activity_id"]
            retrieved_activity_ids = activity["retrieved_activity_ids"]
            activity["retriever_strict_recall"] = 1.0 if expected_activity_id in retrieved_activity_ids else 0.0
            activity["text"] = id

        retrieved_activities_stepwise_str = [
            f"{a['expected_activity_class_name']} : {a['retrieval_accuracy']} : {a['text']}" for a in activities_per_step.values()
        ]
        print(div_template.format(title="retrieved_activities_accuracy_stepwise", content=html.escape("\n".join(retrieved_activities_stepwise_str))))

        retriever_strict_recall = sum(activity["retriever_strict_recall"] for activity in activities_per_step.values()) / len(activities_per_step)
        retrieval_strict_recalls.append(retriever_strict_recall)
        print("activity retrieval recall", str(retriever_strict_recall))

    if use_retrieval:
        print(div_template.format(title="retrieved_triggers", content=html.escape(retrieved_triggers_str)))
        print(div_template.format(title="retrieved_activities", content=html.escape(retrieved_activities_str)))
    if use_generation:
        print(div_template.format(title="generation_prompt", content=html.escape(result["generation_prompt"])))
        print(div_template.format(title="generation_usage", content=html.escape(str(result["generation_usage"]))))
        if mode == "sequence":
            print(div_template.format(title="sequence_raw", content=html.escape(result["sequence_result"]["workflow_raw"])))
            print(div_template.format(title="sequence_valid", content=html.escape(result["sequence_result"]["workflow_valid"])))
            print(div_template.format(title="sequence_used_activities", content=html.escape("\n".join(result["sequence_result"]["used_activities"]))))
            print(div_template.format(title="sequence_used_packages", content=html.escape("\n".join(result["sequence_result"]["used_packages"]))))

            used_variable_content = yaml_dump(result["sequence_result"]["used_variables"])
            used_local_variables_content = yaml_dump(result["sequence_result"]["used_local_variables"])
            used_namespaces_content = yaml_dump(result["sequence_result"]["used_namespaces"])
            print(div_template.format(title="sequence_used_variables", content=html.escape(used_variable_content)))
            print(div_template.format(title="sequence_used_local_variables", content=html.escape(used_local_variables_content)))
            print(div_template.format(title="sequence_used_namespaces", content=html.escape(used_namespaces_content)))

        print(div_template.format(title="workflow_raw", content=html.escape(result["workflow_result"]["workflow_raw"])))
        print(div_template.format(title="workflow_valid", content=html.escape(result["workflow_result"]["workflow_valid"])))
        print(div_template.format(title="workflow_used_trigger", content=html.escape(result["workflow_result"]["used_trigger"] or "")))
        print(div_template.format(title="workflow_used_activities", content=html.escape("\n".join(result["workflow_result"]["used_activities"]))))
        print(div_template.format(title="workflow_used_packages", content=html.escape("\n".join(result["workflow_result"]["used_packages"]))))

        used_variable_content = yaml_dump(result["workflow_result"]["used_variables"])
        used_local_variables_content = yaml_dump(result["workflow_result"]["used_local_variables"])
        used_namespaces_content = yaml_dump(result["workflow_result"]["used_namespaces"])
        print(div_template.format(title="workflow_used_variables", content=html.escape(used_variable_content)))
        print(div_template.format(title="workflow_used_local_variables", content=html.escape(used_local_variables_content)))
        print(div_template.format(title="workflow_used_namespaces", content=html.escape(used_namespaces_content)))

    workflow_key = "workflow_result" if mode == "workflow" else "sequence_result"
    res = result[workflow_key]["workflow_valid"]
    res_yaml = yaml_load(res)
    clean_res_yaml = cleanup_rpa_wf_workflow_for_compare(copy.deepcopy(res_yaml))
    clean_res = yaml_dump(clean_res_yaml)
    print(div_template.format(title="workflow_cleaned", content=html.escape(clean_res)))

    current_log["result"] = result
    if text in dataset:
        sample = dataset[text]

        current_log["data"] = dataset[text]
        sample_activities = get_sample_activities(sample, mode)
        current_log["sample_activities"] = sample_activities

        print("<pre>")
        print("Ground Truth Filename: ", dataset[text]["filename"])  # we could wrap this into div_template as well
        print("</pre>")

        if "plan" in sample:
            print(div_template.format(title="plan", content=html.escape(result["plan"])))
            print(div_template.format(title="ideal_plan", content=html.escape(sample["plan"])))
            print(div_template.format(title="plan_existing", content=html.escape(sample.get("plan_existing", "N/A"))))
            print(div_template.format(title="plan_sequence_ideal", content=html.escape(sample.get("plan_sequence", "N/A"))))
            print(div_template.format(title="plan_whole", content=html.escape(result["plan_whole"])))
            print("<pre>")
            true_plan = sample["plan"] if mode == "workflow" else renumber_plan_steps(sample["plan_sequence"])
            pred_plan = result["plan"] if mode == "workflow" else renumber_plan_steps(result["plan"])
            plan_tree_edit_score = planning_edit_score(true_plan, pred_plan, 0.2, cross_encoder_model)
            plan_tree_edit_scores.append(plan_tree_edit_score)
            plan_score = Levenshtein.ratio(true_plan, pred_plan)
            plan_scores.append(plan_score)
            print("PLAN SCORE", plan_score)
            print("PLAN Tree Edit Score", plan_tree_edit_score)
            print("</pre>")
            current_log["plan_score"] = plan_score
            current_log["plan_tree_edit_score"] = plan_tree_edit_score

        if use_generation:
            if mode == "workflow":
                trigger = get_sample_trigger(sample)
                trigger_n = normalize_activity_dep(trigger)
                trigger_recall = recall([trigger_n], retrieved_triggers)
                trigger_recalls.append(trigger_recall)
                trigger_iou = iou([trigger_n], retrieved_triggers)
                trigger_ious.append(trigger_iou)
                print(div_template.format(title="expected trigger", content=html.escape(trigger)))
                print(div_template.format(title="retrieved triggers", content=html.escape("\n".join(retrieved_triggers))))
                print("<pre>")
                print("trigger recall", trigger_recall)
                print("trigger iou", trigger_iou)
                print("</pre>")

                current_log["trigger_recall"] = trigger_recall
                current_log["trigger_iou"] = trigger_iou

            sample_activities_n = [normalize_activity_dep(a) for a in sample_activities]
            activities_recall = recall(sample_activities_n, retrieved_activities)
            activities_recalls.append(activities_recall)
            activities_iou = iou(sample_activities_n, retrieved_activities)
            activities_ious.append(activities_iou)
            print(div_template.format(title="expected activities", content=html.escape("\n".join(sample_activities_n))))
            print(div_template.format(title="retrieved activities", content=html.escape("\n".join(retrieved_activities))))

            current_log["sample_activities"] = sample_activities
            current_log["retrieved_activities"] = retrieved_activities

            print("<pre>")
            print("activities recall", activities_recall)
            print("activities iou", activities_iou)
            print("</pre>")
            current_log["activities_recall"] = activities_recall
            current_log["activities_iou"] = activities_iou

            if mode == "workflow":
                gt_workflow_yaml = dataset[text]["process"]
            else:
                gt_workflow_yaml = {"workflow": dataset[text]["process_sequence"]}
            gt_workflow = yaml_dump(gt_workflow_yaml)
            clean_gt_workflow_yaml = cleanup_rpa_wf_workflow_for_compare(copy.deepcopy(gt_workflow_yaml))
            clean_gt_workflow = yaml_dump(clean_gt_workflow_yaml)

            workflow_activity_typedefs, _ = parse_workflow_conversion_typedefs(sample["typedefs"])
            try:
                gt_workflow_obj = Workflow("", "", gt_workflow_yaml)
                gt_workflow_synopsis = SynopsisBuilder(workflow_activity_typedefs).build(gt_workflow_obj)
            except Exception as e:
                # assign exception traceback as str
                gt_workflow_synopsis = traceback.format_exc() + "\n" + str(e)

            print(div_template.format(title="ideal_workflow_synopsis", content=html.escape(gt_workflow_synopsis)))
            print(div_template.format(title="ideal_workflow_cleaned", content=html.escape(clean_gt_workflow)))

            try:
                pred_workflow_obj = Workflow("", "", res_yaml)
                pred_workflow_synopsis = SynopsisBuilder(workflow_activity_typedefs).build(pred_workflow_obj)
            except Exception as e:
                pred_workflow_synopsis = traceback.format_exc() + "\n" + str(e)
            print(div_template.format(title="generated_workflow_synopsis", content=html.escape(pred_workflow_synopsis)))
            print(div_template.format(title="generated_workflow_cleaned", content=html.escape(clean_res)))

            res = yaml_dump(res_yaml)
            score = Levenshtein.ratio(res, gt_workflow)
            score_no_extras = Levenshtein.ratio(clean_res, clean_gt_workflow)
            use_typeid_in_activities(clean_gt_workflow_yaml)
            use_typeid_in_activities(clean_res_yaml)
            use_normalized_activity_dep(clean_gt_workflow_yaml)
            use_normalized_activity_dep(clean_res_yaml)
            good, total = ActivityTreeAccuracyCalculator().tree_accuracy(clean_gt_workflow_yaml, clean_res_yaml)
            accuracy = float(good) / total
            try:
                ted_score, _, _, ted_mapping = core_wf_workflow_edit_score(clean_gt_workflow_yaml, clean_res_yaml)
            except Exception as e:
                print("ERROR computing TED score, using 0 value", str(e))
                ted_score = 0.0
                ted_mapping = []
            print(
                div_template.format(
                    title="workflow_edit_mapping TED",
                    content=html.escape("\n".join([f"same {m[0]}" if same_activity_in_mapping(m) else f"distinct {m}" for m in ted_mapping[1:]])),
                )
            )
            params_eval = {
                "pled": {
                    "param_match": "levenshtein",
                    "filter_params": None,
                },
                "peed": {
                    "param_match": "exact",
                    "filter_params": None,
                },
                "eled": {
                    "param_match": "levenshtein",
                    "filter_params": "expression",
                },
                "eeed": {
                    "param_match": "exact",
                    "filter_params": "expression",
                },
            }
            for k, c in params_eval.items():
                try:
                    score, _, _, mapping = core_wf_workflow_edit_score(
                        clean_gt_workflow_yaml, clean_res_yaml, param_match=c["param_match"], filter_params=c["filter_params"]
                    )
                except Exception:
                    print(f"ERROR computing {k} score, using 0 value")
                    score = 0.0
                    mapping = []
                print(
                    div_template.format(
                        title=f"workflow_edit_mapping {k}",
                        content=html.escape("\n".join([f"same {m}" if same_activity_in_mapping(m) else f"distinct {m}" for m in mapping[1:]])),
                    )
                )
                c["current_params_scores"] = [m[3] for m in mapping if m[3] >= 0]
                c["current_params_score"] = sum(c["current_params_scores"]) / len(c["current_params_scores"]) if c["current_params_scores"] else -1.0
                c["overall_score"] = score

            current_log["score"] = {
                "generation_score": score,
                "generation_score_no_extra": score_no_extras,
                "ted_score": ted_score,
                "accuracy": accuracy,
                "pled_score": params_eval["pled"]["overall_score"],
                "peed_score": params_eval["peed"]["overall_score"],
                "eled_score": params_eval["eled"]["overall_score"],
                "eeed_score": params_eval["eeed"]["overall_score"],
            }

            scores.append(score)
            scores_no_extras.append(score_no_extras)
            accuracies.append(accuracy)
            ted_scores.append(ted_score)
            levenshtein_params_score.extend(params_eval["pled"]["current_params_scores"])
            exact_params_score.extend(params_eval["peed"]["current_params_scores"])
            levenshtein_expressions_score.extend(params_eval["eled"]["current_params_scores"])
            exact_expressions_score.extend(params_eval["eeed"]["current_params_scores"])
            pled_scores.append(params_eval["pled"]["overall_score"])
            peed_scores.append(params_eval["peed"]["overall_score"])
            eled_scores.append(params_eval["eled"]["overall_score"])
            eeed_scores.append(params_eval["eeed"]["overall_score"])
            print("<pre>")
            print("GENERATION SCORE", score)
            print("GENERATION SCORE NO EXTRAS", score_no_extras)
            print("ACTIVITY TREE ACCURACY", accuracy, f"{good} / {total}")
            print("ACTIVITY TREE EDIT DISTANCE SCORE", ted_score)
            print("LEVENSHTEIN PARAMS SCORE", params_eval["pled"]["current_params_score"])
            print("EXACT PARAMS SCORE", params_eval["pled"]["current_params_score"])
            print("LEVENSHTEIN EXPRESSIONS SCORE", params_eval["eled"]["current_params_score"])
            print("EXACT EXPRESSIONS SCORE", params_eval["eeed"]["current_params_score"])
            print("TREE EDIT DISTANCE WITH LEVENSHTEIN PARAMS SCORE", params_eval["pled"]["overall_score"])
            print("TREE EDIT DISTANCE WITH EXACT PARAMS SCORE", params_eval["peed"]["overall_score"])
            print("TREE EDIT DISTANCE WITH LEVENSHTEIN EXPRESSIONS SCORE", params_eval["eled"]["overall_score"])
            print("TREE EDIT DISTANCE WITH EXACT EXPRESSIONS SCORE", params_eval["eeed"]["overall_score"])
        print("</pre>")
    else:
        print("Text not found in evalset:", text)
    print("<pre>")
    print(html.escape(yaml_dump(validation)))
    print("</pre>")
    print("elapsed time", elapsed_time)
