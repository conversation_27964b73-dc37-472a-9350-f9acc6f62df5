{"cells": [{"cell_type": "code", "execution_count": 2, "id": "292d86e3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Text content length: 7410\n", "# Process Design Document (PDD)\n", "\n", "**Process Name:** Loan Processing Workflow\n", "**Version:** 1.0\n", "**Author:** <PERSON>\n", "**Date:** 25 Jun 2025\n", "\n", "## Table of Contents\n", "\n", "1. Objective\n", "2. <PERSON><PERSON>\n", "3. Stakeholders & Actors\n", "4. Definitions & Acronyms\n", "5. High‑Level Process Overview\n", "6. Detailed Process Description\n", "   * 6.1 Application Intake & Completeness Validation\n", "   * 6.2 Data Entry & Validation Checks\n", "   * 6.3 Loan Eligibility Analysis & Determination\n", "   * 6.4 System Updates & Communication\n", "7. Exception Handling & Escalations\n", "8. Business Rules & Validation Matrix\n", "9. Service‑Level Agreements (SLAs) & Timers\n", "10. Assumptions & Constraints\n", "11. Non‑Functional Requirements\n", "12. Compliance & Audit Considerations\n", "13. References\n", "14. <PERSON><PERSON><PERSON><PERSON> (BPMN Diagram, Field Mappings, State‑Specific Requirements)\n", "\n", "## 1. Objective\n", "\n", "To define the end‑to‑end automated workflow that processes a new loan application from intake to applicant notification, ensuring compliance with state regulations, data accuracy across integrated systems, and timely communication.\n", "\n", "## 2. <PERSON><PERSON>\n", "\n", "* **In‑Scope:** Residential loan applications submitted via Salesforce, data extraction from supporting documents, validations, LOS updates, eligibility analysis, contract generation, CRM updates, and applicant notifications.\n", "* **Out‑of‑Scope:** Post‑closing activities, loan servicing, secondary market sale processes, manual back‑office audits beyond defined human review steps.\n", "\n", "## 3. Stakeholders & Actors\n", "\n", "| Role | Responsibility |\n", "| --- | --- |\n", "| **Applicant** | Submits required documents, responds to information requests. |\n", "| **<PERSON><PERSON> (Human Review)** | Reviews escalated validation issues & borderline eligibility cases. |\n", "| **Compliance Analyst (Human Review)** | Confirms adherence to state regulations. |\n", "| **RPA Bot – Intake** | Monitors Salesforce for new applications, launches extraction & validation sub‑flows. |\n", "| **RPA Bot – LOS Updater** | Writes validated data to the Loan Origination System (LOS). |\n", "| **RPA Bot – Analytics** | Performs risk calculations & retrieves transaction history concurrently. |\n", "| **CRM Service** | Registers loan opportunity & personalizes applicant responses. |\n", "| **Notification Service** | Sends email/SMS notifications. |\n", "\n", "## 4. Definitions & Acronyms\n", "\n", "| Term | Definition |\n", "| --- | --- |\n", "| **LOS** | Loan Origination System |\n", "| **KYC** | Know Your Customer |\n", "| **BPMN** | Business Process Model and Notation |\n", "| **RPA** | Robotic Process Automation |\n", "\n", "## 5. High‑Level Process Overview\n", "\n", "1. **Trigger:** New loan application created in Salesforce.\n", "2. **Stage 1:** Application intake, data extraction & completeness validation.\n", "3. **Stage 2:** Data entry into LOS & concurrent validation checks.\n", "4. **Stage 3:** Eligibility analysis & determination.\n", "5. **Stage 4:** System updates, applicant communication, and process termination.\n", "\n", "*A BPMN 2.0 diagram is provided in* ***Appendix A****.*\n", "\n", "## 6. Detailed Process Description\n", "\n", "### 6.1 Application Intake & Completeness Validation\n", "\n", "| Item | Description |\n", "| --- | --- |\n", "| **Start Event** | **Message Start Event** – Salesforce “LoanApplicationCreated” record. |\n", "| **Activities** | 1. **Data Extraction (Service Task):** Use AI Document Understanding to extract key data from complex documents (e.g., bank statements). |\n", "\n", "1. **Create/Link Customer Record (Service Task):** Ensure applicant exists as a reference object in Salesforce; create if absent.\n", "2. **Validate Documents (Sub‑Process):** Compare extracted data & uploaded documents against state‑specific completeness & validity rules. | | **Gateways** | **Exclusive Gateway – Validation Result**:\n", "   • *Valid & Complete* → Proceed to Stage 2.\n", "   • *Invalid/Missing* → **Escalate for Human Review** (User Task)\n", "   – If reviewer confirms issue, send “Additional Info Required” request.\n", "   – **Event‑Based Gateway:** Wait for **Message Intermediate Catch Event** (Updated Docs Received) **OR** **Timer Intermediate Catch Event** (5 days). | | **Escalations** | If timer elapses without response, process **ends** with status *Terminated – No Response*. |\n", "\n", "### 6.2 Data Entry & Validation Checks\n", "\n", "| Item | Description |\n", "| --- | --- |\n", "| **Pre‑condition** | Application marked *Complete & Valid*. |\n", "| **Parallel Gateway** | Split into three concurrent paths: |\n", "\n", "1. **Update LOS (Service Task)** – Write applicant & loan data.\n", "2. **Risk Calculations (Service Task)** – Calculate DTI, credit score banding, collateral LTV, etc.\n", "3. **Retrieve Transaction History (Service Task)** – Pull prior dealings & payment history. | | **Join <PERSON>** | **<PERSON><PERSON><PERSON> Join** waits for all paths to finish. |\n", "\n", "### 6.3 Loan Eligibility Analysis & Determination\n", "\n", "| Item | Description |\n", "| --- | --- |\n", "| **Eligibility Engine (Service Task)** | Execute underwriting rules & scoring model. |\n", "| **Human Review (User Task – Optional)** | Triggered when score within ±5% of approval threshold or rules flagged. |\n", "| **Exclusive Gateway – Decision** |  |\n", "\n", "• **Approved** → Update LOS with approval, generate contract (Service Task).\n", "• **Declined** → Mark application *Declined*. |\n", "\n", "### 6.4 System Updates & Communication\n", "\n", "| Item | Description |\n", "| --- | --- |\n", "| **CRM Update (Service Task)** | Register/Update loan opportunity record. |\n", "| **Tailor Response (Service Task)** | Compose personalized approval/decline message. |\n", "| **Notify Applicant (Service Task)** | Send email & SMS via Notification Service. |\n", "| **End Event** | **Message End Event** – Status sent to applicant & systems. |\n", "\n", "## 7. Exception Handling & Escalations\n", "\n", "* **Document Extraction Failures:** Route to Loan Officer for manual keying.\n", "* **System Outages (Salesforce/LOS):** Retry up to 3 times with 10‑minute intervals, else create incident ticket & notify support.\n", "* **High Fraud Risk Scores:** Auto‑escalate to Compliance Analyst.\n", "\n", "## 8. Business Rules & Validation Matrix (Excerpt)\n", "\n", "| Rule ID | Description | Passed When |\n", "| --- | --- | --- |\n", "| VR‑001 | Proof of income provided | Bank statements contain net salary entries for last 3 months |\n", "| VR‑028 | State‑specific ID validity | ID expiration date ≥ current date |\n", "| EL‑101 | Min Credit Score | ≥ 640 |\n", "\n", "*(Full matrix in* ***Appendix B****)*\n", "\n", "## 9. SLAs & Timers\n", "\n", "| Metric | Target |\n", "| --- | --- |\n", "| Intake validation turnaround | ≤ 15 minutes |\n", "| Applicant response wait | **Timer:** 5 days (automatic termination) |\n", "| Eligibility decision after complete app | ≤ 2 hours |\n", "\n", "## 10. Assumptions & Constraints\n", "\n", "* Salesforce & LOS APIs are available and authenticated.\n", "* Document Understanding ML models achieve ≥ 90% extraction accuracy.\n", "* State regulatory requirements provided are current and accurate.\n", "\n", "## 11. Non‑Functional Requirements\n", "\n", "* **Auditability:** Full trace logs of data changes & decision outcomes.\n", "* **Security:** PII encrypted in transit & at rest (TLS 1.3, AES‑256).\n", "* **Performance:** Workflow handles 500 concurrent applications.\n", "\n", "## 12. Compliance & Audit Considerations\n", "\n", "* Retain documents & decision logs for 7 years.\n", "* Align with Fair Lending (Equal Credit Opportunity Act) guidelines.\n", "\n", "## 13. References\n", "\n", "* State Regulatory Requirements Handbook v2025.1\n", "* Salesforce Object Schema – LoanApplication v3.2\n", "* LOS Data Dictionary v2025‑Q2\n", "\n", "## 14. <PERSON><PERSON><PERSON><PERSON>\n", "\n", "**Appendix A:** BPMN 2.0 diagram (placeholder – to be created in BPMN modeller).\n", "**Appendix B:** Detailed Validation & Eligibility Rule Matrix.\n", "**Appendix C:** Field Mapping Tables (Salesforce ↔ LOS ↔ CRM).\n", "\n", "*End of Document*\n"]}], "source": ["import tempfile\n", "import os\n", "from markitdown import MarkItDown\n", "\n", "\n", "# Create a temporary file with the content\n", "with open(\"Loan Processing Pdd V1.docx\", \"rb\") as f:\n", "# with open(\"Invoice_Processing_PDD_Full (1).docx\", \"rb\") as f:\n", "    document_bytes = f.read()\n", "\n", "# Create temp file for MarkItDown\n", "temp_file = tempfile.NamedTemporaryFile(suffix=\".docx\", delete=False)\n", "temp_file.write(document_bytes)\n", "temp_file.flush()\n", "temp_file.close()\n", "\n", "try:\n", "    # Use MarkItDown\n", "    md = MarkItDown()\n", "    result = md.convert(temp_file.name)\n", "    print(f\"Text content length: {len(result.text_content) if result.text_content else 0}\")\n", "    print(result.text_content)\n", "except Exception as e:\n", "    print(f\"Error: {e}\")\n", "finally:\n", "    # Clean up\n", "    if os.path.exists(temp_file.name):\n", "        os.unlink(temp_file.name)"]}], "metadata": {"kernelspec": {"display_name": "ml", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}