{"cells": [{"cell_type": "code", "execution_count": 6, "id": "4ed4cf48", "metadata": {}, "outputs": [], "source": ["import json\n", "import json_repair\n", "\n", "source = '{\\n  \"plan\": {\\n    \"add\": [\\n      {\\n        \"type\": \"bpmn:userTask\",\\n        \"plan_id\": \"CreatePurchaseRequisition\",\\n        \"description\": \"添加一个名为\"创建采购申请\"的用户任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:userTask\",\\n        \"plan_id\": \"ReviewAndApproveRequisition\",\\n        \"description\": \"添加一个名为\"审核并批准申请\"的用户任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:exclusiveGateway\",\\n        \"plan_id\": \"RequisitionApprovedGateway\",\\n        \"description\": \"添加一个名为\"申请是否批准？\"的排他网关\"\\n      },\\n      {\\n        \"type\": \"bpmn:userTask\",\\n        \"plan_id\": \"CreatePurchaseOrder\",\\n        \"description\": \"添加一个名为\"创建采购订单\"的用户任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:sendTask\",\\n        \"plan_id\": \"SendPOToSupplier\",\\n        \"description\": \"添加一个名为\"向供应商发送采购订单\"的发送任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:receiveTask\",\\n        \"plan_id\": \"ReceiveGoods\",\\n        \"description\": \"添加一个名为\"接收货物\"的接收任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:serviceTask\",\\n        \"plan_id\": \"PerformQualityCheck\",\\n        \"description\": \"添加一个名为\"执行质量检查\"的服务任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:exclusiveGateway\",\\n        \"plan_id\": \"QualityCheckPassedGateway\",\\n        \"description\": \"添加一个名为\"质量检查是否通过？\"的排他网关\"\\n      },\\n      {\\n        \"type\": \"bpmn:sendTask\",\\n        \"plan_id\": \"ReturnGoodsToSupplier\",\\n        \"description\": \"添加一个名为\"向供应商退回货物\"的发送任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:receiveTask\",\\n        \"plan_id\": \"ReceiveInvoice\",\\n        \"description\": \"添加一个名为\"接收发票\"的接收任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:userTask\",\\n        \"plan_id\": \"ProcessPayment\",\\n        \"description\": \"添加一个名为\"处理付款\"的用户任务\"\\n      },\\n      {\\n        \"type\": \"bpmn:endEvent\",\\n        \"plan_id\": \"ProcessComplete\",\\n        \"description\": \"添加一个名为\"流程完成\"的结束事件\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-0\",\\n        \"description\": \"添加从开始事件到\"创建采购申请\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-1\",\\n        \"description\": \"添加从\"创建采购申请\"到\"审核并批准申请\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-2\",\\n        \"description\": \"添加从\"审核并批准申请\"到\"申请是否批准？\"网关的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-3\",\\n        \"description\": \"添加从\"申请是否批准？\"网关到\"创建采购订单\"的顺序流，标签为\"是\"，条件表达式为\"是\"\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-4\",\\n        \"description\": \"添加从\"申请是否批准？\"网关到\"创建采购申请\"的顺序流，标签为\"否\"，条件表达式为\"否\"\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-5\",\\n        \"description\": \"添加从\"创建采购订单\"到\"向供应商发送采购订单\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-6\",\\n        \"description\": \"添加从\"向供应商发送采购订单\"到\"接收货物\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-7\",\\n        \"description\": \"添加从\"接收货物\"到\"执行质量检查\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-8\",\\n        \"description\": \"添加从\"执行质量检查\"到\"质量检查是否通过？\"网关的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-9\",\\n        \"description\": \"添加从\"质量检查是否通过？\"网关到\"向供应商退回货物\"的顺序流，标签为\"否\"，条件表达式为\"否\"\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-10\",\\n        \"description\": \"添加从\"质量检查是否通过？\"网关到\"接收发票\"的顺序流，标签为\"是\"，条件表达式为\"是\"\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-11\",\\n        \"description\": \"添加从\"向供应商退回货物\"到\"接收货物\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-12\",\\n        \"description\": \"添加从\"接收发票\"到\"处理付款\"的顺序流\"\\n      },\\n      {\\n        \"type\": \"bpmn:sequenceFlow\",\\n        \"plan_id\": \"seq-13\",\\n        \"description\": \"添加从\"处理付款\"到\"流程完成\"的顺序流\"\\n      }\\n    ],\\n    \"update\": [],\\n    \"delete\": []\\n  },\\n  \"explanation\": \"该计划创建了一个完整的采购到付款流程，包括从采购申请、审批、创建采购订单、向供应商发送订单、接收货物、质量检查、处理不合格品、接收发票到最终付款的所有关键步骤。流程中包含两个决策点：申请审批和质量检查，并为不合格情况设计了循环路径。\",\\n  \"relevant_elements\": [\"bpmn:userTask\", \"bpmn:sendTask\", \"bpmn:receiveTask\", \"bpmn:serviceTask\", \"bpmn:exclusiveGateway\", \"bpmn:endEvent\", \"bpmn:sequenceFlow\"]\\n}'"]}, {"cell_type": "code", "execution_count": 8, "id": "33a4d498", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["{'plan': {'add': [{'type': 'bpmn:userTask', 'plan_id': 'CreatePurchaseRequisition', 'description': '添加一个名为\"创建采购申请\"的用户任务'}, {'type': 'bpmn:userTask', 'plan_id': 'ReviewAndApproveRequisition', 'description': '添加一个名为\"审核并批准申请\"的用户任务'}, {'type': 'bpmn:exclusiveGateway', 'plan_id': 'RequisitionApprovedGateway', 'description': '添加一个名为\"申请是否批准？\"的排他网关'}, {'type': 'bpmn:userTask', 'plan_id': 'CreatePurchaseOrder', 'description': '添加一个名为\"创建采购订单\"的用户任务'}, {'type': 'bpmn:sendTask', 'plan_id': 'SendPOToSupplier', 'description': '添加一个名为\"向供应商发送采购订单\"的发送任务'}, {'type': 'bpmn:receiveTask', 'plan_id': 'ReceiveGoods', 'description': '添加一个名为\"接收货物\"的接收任务'}, {'type': 'bpmn:serviceTask', 'plan_id': 'PerformQualityCheck', 'description': '添加一个名为\"执行质量检查\"的服务任务'}, {'type': 'bpmn:exclusiveGateway', 'plan_id': 'QualityCheckPassedGateway', 'description': '添加一个名为\"质量检查是否通过？\"的排他网关'}, {'type': 'bpmn:sendTask', 'plan_id': 'ReturnGoodsToSupplier', 'description': '添加一个名为\"向供应商退回货物\"的发送任务'}, {'type': 'bpmn:receiveTask', 'plan_id': 'ReceiveInvoice', 'description': '添加一个名为\"接收发票\"的接收任务'}, {'type': 'bpmn:userTask', 'plan_id': 'ProcessPayment', 'description': '添加一个名为\"处理付款\"的用户任务'}, {'type': 'bpmn:endEvent', 'plan_id': 'ProcessComplete', 'description': '添加一个名为\"流程完成\"的结束事件'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-0', 'description': '添加从开始事件到\"创建采购申请\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-1', 'description': '添加从\"创建采购申请\"到\"审核并批准申请\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-2', 'description': '添加从\"审核并批准申请\"到\"申请是否批准？\"网关的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-3', 'description': '添加从\"申请是否批准？\"网关到\"创建采购订单\"的顺序流，标签为\"是\"，条件表达式为\"是\"'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-4', 'description': '添加从\"申请是否批准？\"网关到\"创建采购申请\"的顺序流，标签为\"否\"，条件表达式为\"否\"'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-5', 'description': '添加从\"创建采购订单\"到\"向供应商发送采购订单\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-6', 'description': '添加从\"向供应商发送采购订单\"到\"接收货物\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-7', 'description': '添加从\"接收货物\"到\"执行质量检查\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-8', 'description': '添加从\"执行质量检查\"到\"质量检查是否通过？\"网关的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-9', 'description': '添加从\"质量检查是否通过？\"网关到\"向供应商退回货物\"的顺序流，标签为\"否\"，条件表达式为\"否\"'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-10', 'description': '添加从\"质量检查是否通过？\"网关到\"接收发票\"的顺序流，标签为\"是\"，条件表达式为\"是\"'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-11', 'description': '添加从\"向供应商退回货物\"到\"接收货物\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-12', 'description': '添加从\"接收发票\"到\"处理付款\"的顺序流'}, {'type': 'bpmn:sequenceFlow', 'plan_id': 'seq-13', 'description': '添加从\"处理付款\"到\"流程完成\"的顺序流'}], 'update': [], 'delete': []}, 'explanation': '该计划创建了一个完整的采购到付款流程，包括从采购申请、审批、创建采购订单、向供应商发送订单、接收货物、质量检查、处理不合格品、接收发票到最终付款的所有关键步骤。流程中包含两个决策点：申请审批和质量检查，并为不合格情况设计了循环路径。', 'relevant_elements': ['bpmn:userTask', 'bpmn:sendTask', 'bpmn:receiveTask', 'bpmn:serviceTask', 'bpmn:exclusiveGateway', 'bpmn:endEvent', 'bpmn:sequenceFlow']}\n"]}], "source": ["s = json_repair.loads(source.strip(\"```json\\n\").strip(\"\\n```\"))\n", "print(s)"]}], "metadata": {"kernelspec": {"display_name": "ml", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.12"}}, "nbformat": 4, "nbformat_minor": 5}