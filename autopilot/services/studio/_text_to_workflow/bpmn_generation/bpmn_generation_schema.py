import enum
from datetime import datetime
from typing import Any, List, Optional, Union

import typing_extensions as t
from pydantic import BaseModel, <PERSON>
from typing_extensions import TypedDict

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import ResourceKinds
from services.studio._text_to_workflow.schemas.common import RequestContext
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.request_schema import BaseResponse
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class Tool(enum.Enum):
    GENERATE_BPMN = "generate-bpmn"
    EDIT_BPMN = "edit-bpmn"
    QA = "qa"
    EXTENSION = "extension"
    CONVERT_IMAGE = "convert-image"
    CONVERT_DOCUMENT = "convert-document"
    ACK = "ack"
    UNSUPPORTED = "unsupported"
    GENERATE_EXP = "generate-expression"


class ModelType(enum.Enum):
    OpenAI = "openai"
    Anthropic = "anthropic"
    Google = "google"


class LightModelType(enum.Enum):
    GoogleFlash = "google_flash"


class BpmnGenerationTaskResult(t.TypedDict):
    result: str
    usage: TokenUsage
    prompt_class: str | None


class GenerateBpmnRequest(t.TypedDict):
    userRequest: str
    currentBpmn: t.NotRequired[str | None]


class BpmnGenerationResponse(BaseResponse):
    result: str
    prompt_class: t.NotRequired[str | None]


class BpmnValidationResult(t.TypedDict):
    valid: bool
    error_message: str | None


class ChatHistory(t.TypedDict):
    request: str
    response: str
    tool: str
    timestamp: datetime
    properties: t.NotRequired[dict[str, str] | None]


class ModelOverride(t.TypedDict):
    model_type: ModelType
    tool: t.NotRequired[Tool | None]


class GenerateBpmnChatRequest(GenerateBpmnRequest):
    chatHistory: t.NotRequired[list | None]
    image: t.NotRequired[bytes | None]
    document: t.NotRequired[bytes | None]
    modelTypeOverride: t.NotRequired[ModelOverride | None]


class BpmnGenerationChatTaskResult(t.TypedDict):
    explanation: str
    title: t.NotRequired[str | None]
    add: list | None
    update: list | None
    delete: list | None
    usage: TokenUsage


class BpmnGenerationChatFromImageTaskResult(t.TypedDict):
    title: t.NotRequired[str | None]
    xml: t.NotRequired[str | None]
    explanation: t.NotRequired[str | None]
    usage: TokenUsage


class BpmnGenerationChatResponse(BaseResponse):
    explanation: str
    title: t.NotRequired[str | None]
    add: t.NotRequired[list | None]
    update: t.NotRequired[list | None]
    delete: t.NotRequired[list | None]


class ExtensionDataOverrideRequest(t.TypedDict):
    processes: t.NotRequired[list | None]
    actionApps: t.NotRequired[list | None]
    connections: t.NotRequired[list | None]
    triggers: t.NotRequired[dict[str, list] | None]
    activities: t.NotRequired[dict[str, list] | None]
    queues: t.NotRequired[list | None]


class BpmnAssistantRequest(GenerateBpmnChatRequest):
    routerModel: t.NotRequired[ModelType | None]
    extensionDataOverride: t.NotRequired[ExtensionDataOverrideRequest | None]
    useMultiAgent: t.NotRequired[bool]
    solutionId: t.NotRequired[str | None]
    projectKey: t.NotRequired[str | None]


class ToolRequest(BaseModel):
    tool: Tool | None = Field(description="The tool of the user query. Use the following values: generate-bpmn, edit-bpmn, qa, convert-image, unsupported.")
    request: str | None = Field(
        description="A refined version of the user query. If the query is not valid, leave this empty. Incorporate any useful details from the chat history and attachments into the refined user request."
    )


class BpmnRouterResult(BaseModel):
    explanation: str = Field(
        description="A brief analysis of the user query in the context of the chat history, the current BPMN, and the available entities. Use at most 50 words for this analysis. Think about whether the user query refers to the current BPMN, a BPMN in the chat history, or multiple BPMNs (from the chat history and/or the current BPMN). Make it second person when referring to the user, as we will send them your thoughts. Be friendly and polite, but clear and mindful in your thoughts."
    )
    valid: bool = Field(
        description="A boolean value indicating whether the user query too vague to detect intention, harmful, dangerous, or inappropriate. If not, return true."
    )
    tools: list[ToolRequest] | None = Field(
        description="A list of tools that can be used to process the user query. If the user query is not valid, return an empty list."
    )


class BpmnRouterException(Exception):
    """Exception raised when there is an error in routing the bpmn request."""

    pass


class ToolResult(t.TypedDict):
    tool: Tool
    explanation: str


class BpmnGenerationToolResult(ToolResult):
    title: t.NotRequired[str | None]
    add: list | None
    update: list | None
    delete: list | None


class BaseExtensionInfo(BaseModel):
    id: int
    score: Optional[float] = None


class ExtensionInfo(BaseExtensionInfo):
    name: str
    description: Optional[str] = None


class AgentExtensionInfo(ExtensionInfo):
    external: bool


class ExtensionRequest(BaseModel):
    processes: list[ExtensionInfo] = []
    agents: list[Union[ExtensionInfo, AgentExtensionInfo]] = []
    actionApps: list[ExtensionInfo] = Field(alias="human-in-the-loop", default=[])
    connections: list[ExtensionInfo] = []


class ElementExtensionData(BaseModel):
    extensions: list[BaseExtensionInfo]


class ElementExtensionInfo(BaseModel):
    id: str
    name: Optional[str] = None
    purpose: Optional[str] = None
    type: str
    data: ElementExtensionData


class ElementExtensionResult(BaseModel):
    explanation: str
    title: str | None = Field(default=None)
    update: list[ElementExtensionInfo] | None = Field(default=list())


class BaseExtensionResponse(BaseModel):
    fakeId: int | None = Field(exclude=True)
    name: str
    type: str
    score: float


class ActionApp(BaseExtensionResponse):
    id: str
    systemName: str
    deployVersion: int


class Connection(BaseModel):
    id: str
    name: str
    isDefault: bool
    state: str
    instanceId: int = Field(exclude=True)


class Activity(BaseModel):
    name: str
    displayName: str
    description: str | None
    objectName: str | None
    eventOperation: str | None = Field(exclude=True)


class ConnectorObject(BaseModel):
    name: str
    displayName: str
    executionType: str = Field(exclude=True)


class Process(BaseExtensionResponse):
    id: str
    description: str | None = Field(exclude=True)
    connection: Optional[Connection] = None
    activity: Optional[ConnectorObject] = None


class Connector(BaseExtensionResponse):
    key: str
    connection: Connection | None
    activity: Activity | None
    trigger: Activity | None
    description: str | None = Field(exclude=True)
    isExternalAgent: Optional[bool] = Field(exclude=True, default=False)
    inputJson: str | None
    resourceType: str | None = Field(exclude=True)


class Folder(TypedDict):
    fullyQualifiedName: str
    folderKey: str
    path: str


class SolutionResource(BaseExtensionResponse):
    id: str
    description: str | None = Field(exclude=True)
    kind: ResourceKinds | None = Field(exclude=True)
    folder: Folder | None = Field(exclude=True)
    inputJson: str | None
    resourceType: str | None = Field(exclude=True)


class SolutionResourceRequest(TypedDict):
    folder: Folder
    key: str
    kind: str
    type: str | None


class ConnectorElement(BaseModel):
    key: str
    name: str
    description: str


class ExtensionData(BaseModel):
    suggestions: List[Process | ActionApp | Connector | SolutionResource]


class ElementExtension(BaseModel):
    id: str
    type: str
    data: ExtensionData


class BpmnExtensionToolResult(ToolResult):
    title: str | None
    update: list | None


class BpmnExtensionInputToolResult(ToolResult):
    inputJson: str | None


class BpmnImageConversionToolResult(ToolResult):
    title: t.NotRequired[str | None]
    xml: str | None


class ExpressionGenerationAnalysisInput(BaseModel):
    currentBpmn: str = Field(description="current bpmn in xml")
    userRequest: str = Field(description="user request")
    chatHistory: dict[Tool, list[ChatHistory]] | None
    override_model_type: ModelType


class ExpressionGenerationResult(BaseModel):
    expression: str
    elementId: str
    dataType: str
    varName: str
    explanation: str
    currentExpression: str


class ExpressionGenerationToolResult(ToolResult):
    expression: str
    elementId: str
    dataType: str
    varName: str
    currentExpression: str


class BpmnAssistantResponse(t.TypedDict):
    valid: bool
    explanation: t.NotRequired[str | None]
    results: t.NotRequired[
        List[Union[ToolResult, BpmnGenerationToolResult, BpmnExtensionToolResult, BpmnImageConversionToolResult, ExpressionGenerationToolResult]] | None
    ]


class ExtensionDataOverride(BaseModel):
    processes: list[Process] | None = None
    actionApps: list[ActionApp] | None = None
    connections: list[Connector] | None = None
    activities: dict[str, list[Activity]] | None = None
    triggers: dict[str, list[Activity]] | None = None
    objects: dict[str, list[ConnectorObject]] | None = None
    queues: list[SolutionResource] | None = None


class BpmnRequestContext(BaseModel):
    # The user request is the user's request
    user_request: str

    # The current bpmn is the current bpmn
    current_bpmn: str

    # Optional image and document data
    image_data: bytes | None = None
    document_data: bytes | None = None

    solution_id: str | None = None

    project_key: str | None = None

    override_model_type: ModelType | None = None
    # is bpmn validation supported, renamed it to support_validation because Base model has a validate method
    support_validation: bool = False
    # The chat history is the chat history of each tool
    chat_history: dict[Tool, list[ChatHistory]] | None = None

    request_context: RequestContext | None

    extension_data_override: ExtensionDataOverride | None = None

    # Performance tracker for telemetry
    performance_tracker: Any | None = None

    extension_input_schema: str | None = None


class TelemetryType(enum.Enum):
    BPMN = "bpmn"
    EXPRESSION = "expression"
    EXTENSIONS = "extensions"


class TelemetryUserAction(enum.Enum):
    ACCEPT = "accept"
    REJECT = "reject"
    LIKE = "like"
    DISLIKE = "dislike"
    TEST = "test"
    TEST_STEP_BY_STEP = "test_step_by_step"
    PUBLISH = "publish"
    USER_REQUEST = "user_request"

    @property
    def can_summarize(self) -> bool:
        return self in {TelemetryUserAction.TEST, TelemetryUserAction.TEST_STEP_BY_STEP, TelemetryUserAction.PUBLISH}


class BpmnTelemetryRequest(t.TypedDict):
    sourceType: TelemetryType
    userAction: list[TelemetryUserAction]
    request: t.NotRequired[str | None]
    response: t.NotRequired[str | None]
    currentBpmn: t.NotRequired[str | None]
    fileId: t.NotRequired[str | None]
    solutionId: t.NotRequired[str | None]
    projectKey: t.NotRequired[str | None]
    chatSessionId: t.NotRequired[str | None]
    customData: t.NotRequired[dict[str, t.Any]]  # Optional custom data for telemetry


class UserRequestTelemetryRequest(BpmnTelemetryRequest):
    tools: list[ToolRequest]


class UserActionExplanationTelemetryResult(t.TypedDict):
    userRequest: str
    aiResponse: str
    explanation: str


class BpmnSummarizationTelemetryResult(t.TypedDict):
    bpmnDescription: str
    domain: str


class UserRequestSummarizationTelemetryResult(t.TypedDict):
    userRequest: str


class ConnectorSuggestion(BaseModel):
    connector: str
    score: float


class ExtensionType(enum.Enum):
    ACTION = "action-app"
    AGENT = "agent"
    RPA_WORKFLOW = "rpa-workflow"
    CONNECTOR = "connector"
    QUEUE = "queue"
    BUSINESS_RULE = "business-rule"
    AGENTIC_PROCESS = "agentic-process"
    API_WORKFLOW = "api-workflow"


class BpmnElement(BaseModel):
    id: str
    name: str
    description: str
    purpose: str
    type: str | None = Field(
        default=None,
        description="The type of the BPMN element, e.g., 'bpmn:startEvent', 'bpmn:endEvent', 'bpmn:task', etc. when extension type is connector, leave it empty if not applicable.",
    )


class ConnectorDirection(enum.Enum):
    INCOMING = "incoming"
    OUTGOING = "outgoing"


class BpmnExtensionPlan(BaseModel):
    element: BpmnElement
    extension_type: ExtensionType
    direction: ConnectorDirection | None = Field(
        default=None,
        description="The direction of the connector, either 'incoming' or 'outgoing'. start event should always be incoming and end event should always be outgoing. If not applicable, leave it empty.",
    )
    connectors: list[ConnectorSuggestion] | None = None
    external_agent: bool | None = Field(
        default=None, description="Whether the extension is an external agent when extension_type is 'agent'. If not applicable, leave it empty."
    )
    wait_for_completion: bool | None = Field(default=None, description="Whether should wait for queue item completion. If not applicable, leave it empty.")


class BpmnExtensionPlanResult(BaseModel):
    relevant_elements: list[BpmnExtensionPlan] | None = None
    title: str | None = None
    explanation: str
    error: str | None = None


class ElementImplementationResult(BaseModel):
    suggestions: list[BaseExtensionInfo] | None = None
    explanation: str | None = None
    error: str | None = None


class ElementExtensionItem(BaseModel):
    element: BpmnElement
    suggestions: List[Connector | SolutionResource]


class ExtensionInputRequest(BaseModel):
    extension_type: ExtensionType
    items: list[ElementExtensionItem]
