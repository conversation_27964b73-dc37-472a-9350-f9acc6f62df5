import enum
from typing import List, TypedDict, Union

import typing_extensions as t
from pydantic import BaseModel, Field

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionToolResult,
    BpmnGenerationToolResult,
    BpmnImageConversionToolResult,
    BpmnRequestContext,
    ExpressionGenerationToolResult,
    ExtensionDataOverrideRequest,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class ProcessType(enum.Enum):
    BPMN = "bpmn"
    DMN = "dmn"


class BusinessProcessAssistantRequest(BaseModel):
    userRequest: str
    processType: ProcessType
    currentProcess: str | None = None
    image: bytes | None = None
    document: bytes | None = None
    chatHistory: list | None = None
    sessionId: str | None = None
    solutionId: str | None = None
    projectKey: str
    modelProvider: ModelType | None = None
    # Used for extension evals
    extensionDataOverride: ExtensionDataOverrideRequest | None = None


class BusinessProcessAssistantToolRequest(BaseModel):
    tool: Tool | None = Field(
        description="The tool of the user query. Use the following values: generate-bpmn, edit-bpmn, qa, convert-image, extension, generate-expression, unsupported."
    )
    request: str | None = Field(
        description="A refined version of the user query. If the query is not valid, leave this empty. Incorporate any useful details from the chat history and attachments into the refined user request."
    )
    title: str | None = Field(default=None, description="A title summarizing the current request.")
    reasoning: str | None = Field(
        description="A brief analysis of the current request in the context of the chat history, the current process, and the available entities. Think about whether the user query refers to the current process, a process in the chat history, or multiple BPMNs (from the chat history and/or the current BPMN). Make it second person when referring to the user, as we will send them your thoughts. Be friendly and polite, but clear and mindful in your thoughts."
    )


class BusinessProcessAssistantPlannerResponse(t.TypedDict):
    valid: bool
    reasoning: t.NotRequired[str | None]
    results: t.NotRequired[List[BusinessProcessAssistantToolRequest] | None]


class BusinessProcessAssistantPlannerResult(BaseModel):
    reasoning: str = Field(
        description="A brief analysis of the user query in the context of the chat history, the current process, and the available entities. Think about whether the user query refers to the current process, a process in the chat history, or multiple BPMNs (from the chat history and/or the current BPMN). Make it second person when referring to the user, as we will send them your thoughts. Be friendly and polite, but clear and mindful in your thoughts."
    )
    valid: bool = Field(
        description="A boolean value indicating whether the user query too vague to detect intention, harmful, dangerous, or inappropriate. If not, return true."
    )
    tools: list[BusinessProcessAssistantToolRequest] | None = Field(
        description="A list of tools that can be used to process the user query. If the user query is not valid, return an empty list."
    )


class BusinessProcessAssistantPlannerException(Exception):
    """Exception raised when there is an error in planning the business process request."""

    pass


class BusinessProcessAgentState(TypedDict):
    request: BusinessProcessAssistantRequest | None
    planner_result: BusinessProcessAssistantPlannerResult | None
    context: BpmnRequestContext
    current_tool_index: int
    tool_results: t.NotRequired[
        List[Union[None, ToolResult, BpmnGenerationToolResult, BpmnExtensionToolResult, BpmnImageConversionToolResult, ExpressionGenerationToolResult]] | None
    ]
    usage_token: list[TokenUsage]
