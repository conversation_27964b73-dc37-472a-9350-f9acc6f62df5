# BPMN Process Examples for Document Processing
Here are comprehensive examples showing proper BPMN structure with decision points, subprocesses, cross-participant communication, and all supported elements for document-based content:

## Example 1: Purchase Order Processing with Proper Lane Structure

**Document Type:** PDF Process Documentation

**Extracted Content:**
```
Purchase Order Processing Workflow (From PDF Documentation)
1. Procurement Team receives purchase request from employee
2. Procurement validates request and checks budget approval
3. If budget requires approval, Finance Manager reviews and approves
4. Procurement creates purchase order and sends to vendor
5. <PERSON><PERSON><PERSON> confirms order and provides delivery timeline
6. Receiving Team receives goods and verifies against PO
7. If goods match PO, Receiving creates receipt
8. If goods don't match, Receiving rejects and notifies Procurement
9. Finance processes payment after receipt confirmation
10. Process complete with vendor payment
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Purchase order processing workflow with proper lane structure showing role-based task assignment and clear decision points from PDF documentation.",
    "title": "Purchase Order Processing",
    "add": [
        {
            "type": "bpmn:collaboration",
            "id": "Collaboration_PurchaseOrder",
            "name": "Purchase Order Collaboration"
        },
        {
            "type": "bpmn:participant",
            "id": "Participant_PurchaseOrder",
            "parentId": "Collaboration_PurchaseOrder",
            "name": "Purchase Order Department",
            "data": {
                "processRef": "Process_PurchaseOrder",
                "label": "Main participant for purchase order processing"
            }
        },
        {
            "type": "bpmn:process",
            "id": "Process_PurchaseOrder",
            "name": "Purchase Order Processing"
        },
        {
            "type": "bpmn:laneSet",
            "id": "LaneSet_PurchaseOrder",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:lane",
            "id": "Lane_Procurement",
            "parentId": "LaneSet_PurchaseOrder",
            "name": "Procurement Team",
            "data": {
                "participantId": "Participant_PurchaseOrder"
            }
        },
        {
            "type": "bpmn:lane",
            "id": "Lane_Finance",
            "parentId": "LaneSet_PurchaseOrder",
            "name": "Finance Manager",
            "data": {
                "participantId": "Participant_PurchaseOrder"
            }
        },
        {
            "type": "bpmn:lane",
            "id": "Lane_Receiving",
            "parentId": "LaneSet_PurchaseOrder",
            "name": "Receiving Team",
            "data": {
                "participantId": "Participant_PurchaseOrder"
            }
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_RequestReceived",
            "parentId": "Process_PurchaseOrder",
            "name": "Purchase Request Received",
            "data": {
                "label": "Employee submits purchase request",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ValidateRequest",
            "parentId": "Process_PurchaseOrder",
            "name": "Validate Request",
            "data": {
                "label": "Procurement validates request details and checks budget requirements",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_BudgetApproval",
            "parentId": "Process_PurchaseOrder",
            "name": "Budget Approval Required?",
            "data": {
                "label": "Does the purchase amount require manager approval?",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_BudgetApproval",
            "parentId": "Process_PurchaseOrder",
            "name": "Budget Approval",
            "data": {
                "label": "Finance Manager reviews and approves budget allocation",
                "laneId": "Lane_Finance"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_BudgetDecision",
            "parentId": "Process_PurchaseOrder",
            "name": "Budget Approved?",
            "data": {
                "label": "Did Finance Manager approve the budget?",
                "laneId": "Lane_Finance"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_CreatePO",
            "parentId": "Process_PurchaseOrder",
            "name": "Create Purchase Order",
            "data": {
                "label": "Procurement creates purchase order and sends to vendor",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:receiveTask",
            "id": "Task_VendorConfirmation",
            "parentId": "Process_PurchaseOrder",
            "name": "Receive Vendor Confirmation",
            "data": {
                "label": "Wait for vendor to confirm order and delivery timeline",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ReceiveGoods",
            "parentId": "Process_PurchaseOrder",
            "name": "Receive Goods",
            "data": {
                "label": "Receiving team receives and inspects delivered goods",
                "laneId": "Lane_Receiving"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_GoodsMatch",
            "parentId": "Process_PurchaseOrder",
            "name": "Goods Match PO?",
            "data": {
                "label": "Do received goods match the purchase order specifications?",
                "laneId": "Lane_Receiving"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_CreateReceipt",
            "parentId": "Process_PurchaseOrder",
            "name": "Create Receipt",
            "data": {
                "label": "Receiving creates goods receipt confirmation",
                "laneId": "Lane_Receiving"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_RejectGoods",
            "parentId": "Process_PurchaseOrder",
            "name": "Reject Goods",
            "data": {
                "label": "Receiving rejects goods and notifies procurement",
                "laneId": "Lane_Receiving"
            }
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_ProcessPayment",
            "parentId": "Process_PurchaseOrder",
            "name": "Process Payment",
            "data": {
                "label": "Finance processes vendor payment after receipt confirmation",
                "laneId": "Lane_Finance"
            }
        },
        {
            "type": "bpmn:sendTask",
            "id": "Task_NotifyRejection",
            "parentId": "Process_PurchaseOrder",
            "name": "Notify Procurement",
            "data": {
                "label": "Send rejection notification to procurement team",
                "laneId": "Lane_Receiving"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_OrderComplete",
            "parentId": "Process_PurchaseOrder",
            "name": "Order Complete",
            "data": {
                "label": "Purchase order processed and vendor paid",
                "laneId": "Lane_Finance"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_OrderRejected",
            "parentId": "Process_PurchaseOrder",
            "name": "Order Rejected",
            "data": {
                "label": "Goods rejected, procurement notified",
                "laneId": "Lane_Procurement"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_BudgetDenied",
            "parentId": "Process_PurchaseOrder",
            "name": "Budget Denied",
            "data": {
                "label": "Purchase request denied due to budget constraints",
                "laneId": "Lane_Finance"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Start_Validate",
            "source": "StartEvent_RequestReceived",
            "target": "Task_ValidateRequest",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Validate_Budget",
            "source": "Task_ValidateRequest",
            "target": "Gateway_BudgetApproval",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_RequiresApproval",
            "source": "Gateway_BudgetApproval",
            "target": "Task_BudgetApproval",
            "parentId": "Process_PurchaseOrder",
            "name": "Yes",
            "data": {
                "conditionExpression": "${budgetApprovalRequired == true}",
                "label": "Amount exceeds approval threshold"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_NoApproval",
            "source": "Gateway_BudgetApproval",
            "target": "Task_CreatePO",
            "parentId": "Process_PurchaseOrder",
            "name": "No",
            "data": {
                "conditionExpression": "${budgetApprovalRequired == false}",
                "label": "Within procurement authority"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Approval_Decision",
            "source": "Task_BudgetApproval",
            "target": "Gateway_BudgetDecision",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_BudgetApproved",
            "source": "Gateway_BudgetDecision",
            "target": "Task_CreatePO",
            "parentId": "Process_PurchaseOrder",
            "name": "Approved",
            "data": {
                "conditionExpression": "${budgetApproved == true}",
                "label": "Budget approved by finance"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_BudgetDenied",
            "source": "Gateway_BudgetDecision",
            "target": "EndEvent_BudgetDenied",
            "parentId": "Process_PurchaseOrder",
            "name": "Denied",
            "data": {
                "conditionExpression": "${budgetApproved == false}",
                "label": "Budget not approved"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_PO_Confirmation",
            "source": "Task_CreatePO",
            "target": "Task_VendorConfirmation",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Confirmation_Receive",
            "source": "Task_VendorConfirmation",
            "target": "Task_ReceiveGoods",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Receive_Check",
            "source": "Task_ReceiveGoods",
            "target": "Gateway_GoodsMatch",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_GoodsMatch",
            "source": "Gateway_GoodsMatch",
            "target": "Task_CreateReceipt",
            "parentId": "Process_PurchaseOrder",
            "name": "Match",
            "data": {
                "conditionExpression": "${goodsMatch == true}",
                "label": "Goods match PO specifications"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_GoodsReject",
            "source": "Gateway_GoodsMatch",
            "target": "Task_RejectGoods",
            "parentId": "Process_PurchaseOrder",
            "name": "No Match",
            "data": {
                "conditionExpression": "${goodsMatch == false}",
                "label": "Goods do not match specifications"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Receipt_Payment",
            "source": "Task_CreateReceipt",
            "target": "Task_ProcessPayment",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Payment_Complete",
            "source": "Task_ProcessPayment",
            "target": "EndEvent_OrderComplete",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Reject_Notify",
            "source": "Task_RejectGoods",
            "target": "Task_NotifyRejection",
            "parentId": "Process_PurchaseOrder"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Notify_End",
            "source": "Task_NotifyRejection",
            "target": "EndEvent_OrderRejected",
            "parentId": "Process_PurchaseOrder"
        }
    ],
    "update": [],
    "delete": [
        {
            "id": "Process_1",
            "type": "bpmn:process"
        }
    ]
}
```

## Example 2: PDF PDD - Software Development Lifecycle with Testing Subprocess

**Document Type:** PDF Process Development Document (PDD)

**Extracted Content:**
```
Software Development Process (From PDF PDD)
1. Product Owner creates user story requirements
2. Development Team receives requirements
3. Team Lead assigns tasks to developers
4. Developers implement features
5. Once development complete, initiate testing subprocess:
   - Unit testing by developer
   - Integration testing by QA team
   - User acceptance testing
   - Performance testing
6. If testing fails, return to development
7. If testing passes, deploy to staging
8. Conduct final review
9. Deploy to production
10. Notify stakeholders of completion
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Software development process with comprehensive testing subprocess showing proper boundary flows and decision gateways from PDF PDD extraction.",
    "title": "Software Development Lifecycle",
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_SoftwareDev",
            "name": "Software Development Process"
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_Requirements",
            "parentId": "Process_SoftwareDev",
            "name": "Requirements Created",
            "data": {
                "label": "Product Owner creates user story requirements"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ReceiveRequirements",
            "parentId": "Process_SoftwareDev",
            "name": "Receive Requirements",
            "data": {
                "label": "Development team receives and reviews requirements"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_AssignTasks",
            "parentId": "Process_SoftwareDev",
            "name": "Assign Development Tasks",
            "data": {
                "label": "Team Lead assigns tasks to individual developers"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ImplementFeatures",
            "parentId": "Process_SoftwareDev",
            "name": "Implement Features",
            "data": {
                "label": "Developers implement assigned features"
            }
        },
        {
            "type": "bpmn:subProcess",
            "id": "SubProcess_Testing",
            "parentId": "Process_SoftwareDev",
            "name": "Testing Phase",
            "data": {
                "label": "Comprehensive testing subprocess",
                "isExpanded": true
            }
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_Testing",
            "parentId": "SubProcess_Testing",
            "name": "Begin Testing"
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_UnitTesting",
            "parentId": "SubProcess_Testing",
            "name": "Unit Testing",
            "data": {
                "label": "Developer performs unit testing"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_UnitTestResults",
            "parentId": "SubProcess_Testing",
            "name": "Unit Tests Pass?",
            "data": {
                "label": "Did unit testing pass successfully?"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_IntegrationTesting",
            "parentId": "SubProcess_Testing",
            "name": "Integration Testing",
            "data": {
                "label": "QA team performs integration testing"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_IntegrationResults",
            "parentId": "SubProcess_Testing",
            "name": "Integration Tests Pass?",
            "data": {
                "label": "Did integration testing pass successfully?"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_UATesting",
            "parentId": "SubProcess_Testing",
            "name": "User Acceptance Testing",
            "data": {
                "label": "Business users perform acceptance testing"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_Testing",
            "parentId": "SubProcess_Testing",
            "name": "Testing Complete"
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_TestResults",
            "parentId": "Process_SoftwareDev",
            "name": "Tests Passed?",
            "data": {
                "label": "Did all testing phases pass successfully?"
            }
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_DeployStaging",
            "parentId": "Process_SoftwareDev",
            "name": "Deploy to Staging",
            "data": {
                "label": "Automated deployment to staging environment"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_FinalReview",
            "parentId": "Process_SoftwareDev",
            "name": "Final Review",
            "data": {
                "label": "Conduct final review before production"
            }
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_DeployProduction",
            "parentId": "Process_SoftwareDev",
            "name": "Deploy to Production",
            "data": {
                "label": "Automated deployment to production environment"
            }
        },
        {
            "type": "bpmn:sendTask",
            "id": "Task_NotifyStakeholders",
            "parentId": "Process_SoftwareDev",
            "name": "Notify Stakeholders",
            "data": {
                "label": "Send completion notification to stakeholders"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_Complete",
            "parentId": "Process_SoftwareDev",
            "name": "Development Complete",
            "data": {
                "label": "Software development lifecycle completed"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Start_Receive",
            "source": "StartEvent_Requirements",
            "target": "Task_ReceiveRequirements",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Receive_Assign",
            "source": "Task_ReceiveRequirements",
            "target": "Task_AssignTasks",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Assign_Implement",
            "source": "Task_AssignTasks",
            "target": "Task_ImplementFeatures",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Implement_Testing",
            "source": "Task_ImplementFeatures",
            "target": "SubProcess_Testing",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Testing_Gateway",
            "source": "SubProcess_Testing",
            "target": "Gateway_TestResults",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Failed_Rework",
            "source": "Gateway_TestResults",
            "target": "Task_ImplementFeatures",
            "parentId": "Process_SoftwareDev",
            "name": "Failed",
            "data": {
                "label": "Testing failed, return to development"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Passed_Staging",
            "source": "Gateway_TestResults",
            "target": "Task_DeployStaging",
            "parentId": "Process_SoftwareDev",
            "name": "Passed",
            "data": {
                "label": "All tests passed successfully"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Staging_Review",
            "source": "Task_DeployStaging",
            "target": "Task_FinalReview",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Review_Production",
            "source": "Task_FinalReview",
            "target": "Task_DeployProduction",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Production_Notify",
            "source": "Task_DeployProduction",
            "target": "Task_NotifyStakeholders",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Notify_End",
            "source": "Task_NotifyStakeholders",
            "target": "EndEvent_Complete",
            "parentId": "Process_SoftwareDev"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Sub_Start_Unit",
            "source": "StartEvent_Testing",
            "target": "Task_UnitTesting",
            "parentId": "SubProcess_Testing"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Unit_Gateway",
            "source": "Task_UnitTesting",
            "target": "Gateway_UnitTestResults",
            "parentId": "SubProcess_Testing"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_UnitPass_Integration",
            "source": "Gateway_UnitTestResults",
            "target": "Task_IntegrationTesting",
            "parentId": "SubProcess_Testing",
            "name": "Pass",
            "data": {
                "label": "Unit tests passed, proceed to integration testing"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_UnitFail_End",
            "source": "Gateway_UnitTestResults",
            "target": "EndEvent_Testing",
            "parentId": "SubProcess_Testing",
            "name": "Fail",
            "data": {
                "label": "Unit tests failed, end testing process"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Integration_Gateway",
            "source": "Task_IntegrationTesting",
            "target": "Gateway_IntegrationResults",
            "parentId": "SubProcess_Testing"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_IntegrationPass_UAT",
            "source": "Gateway_IntegrationResults",
            "target": "Task_UATesting",
            "parentId": "SubProcess_Testing",
            "name": "Pass",
            "data": {
                "label": "Integration tests passed, proceed to UAT"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_IntegrationFail_End",
            "source": "Gateway_IntegrationResults",
            "target": "EndEvent_Testing",
            "parentId": "SubProcess_Testing",
            "name": "Fail",
            "data": {
                "label": "Integration tests failed, end testing process"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_UAT_End",
            "source": "Task_UATesting",
            "target": "EndEvent_Testing",
            "parentId": "SubProcess_Testing"
        }
    ]
}
```

## Example 3: DOCX PDD - Cross-Participant Procurement Process

**Document Type:** DOCX Process Development Document

**Extracted Content:**
```
Procurement Process (From DOCX PDD)
Requester Department:
- Identifies need for goods/services
- Creates purchase requisition
- Submits to Procurement Team

Procurement Team:
- Reviews requisition for completeness
- Conducts vendor evaluation process
- Negotiates terms and pricing
- Creates purchase order
- Sends PO to approved vendor

Vendor:
- Receives purchase order
- Confirms availability and delivery dates
- Ships goods/provides services
- Sends invoice to Finance

Finance Department:
- Receives goods receipt from Requester
- Matches invoice with PO and receipt
- Processes payment to vendor
- Updates financial records
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Cross-participant procurement process from DOCX PDD showing collaboration between Requester, Procurement, Vendor, and Finance with proper message flows.",
    "title": "Procurement Process",
    "add": [
        {
            "type": "bpmn:collaboration",
            "id": "Collaboration_Procurement",
            "name": "Procurement Collaboration"
        },
        {
            "type": "bpmn:participant",
            "id": "Participant_Requester",
            "name": "Requester Department",
            "parentId": "Collaboration_Procurement",
            "data": {
                "processRef": "Process_Requester",
                "label": "Department requesting goods/services"
            }
        },
        {
            "type": "bpmn:participant",
            "id": "Participant_Procurement",
            "name": "Procurement Team",
            "parentId": "Collaboration_Procurement",
            "data": {
                "processRef": "Process_Procurement",
                "label": "Procurement team handling vendor management"
            }
        },
        {
            "type": "bpmn:participant",
            "id": "Participant_Vendor",
            "name": "Vendor",
            "parentId": "Collaboration_Procurement",
            "data": {
                "processRef": "Process_Vendor",
                "label": "External vendor providing goods/services"
            }
        },
        {
            "type": "bpmn:participant",
            "id": "Participant_Finance",
            "name": "Finance Department",
            "parentId": "Collaboration_Procurement",
            "data": {
                "processRef": "Process_Finance",
                "label": "Finance team handling payments"
            }
        },
        {
            "type": "bpmn:process",
            "id": "Process_Requester",
            "name": "Requester Process"
        },
        {
            "type": "bpmn:process",
            "id": "Process_Procurement",
            "name": "Procurement Process"
        },
        {
            "type": "bpmn:process",
            "id": "Process_Vendor",
            "name": "Vendor Process"
        },
        {
            "type": "bpmn:process",
            "id": "Process_Finance",
            "name": "Finance Process"
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_Need",
            "parentId": "Process_Requester",
            "name": "Need Identified",
            "data": {
                "label": "Department identifies need for goods/services",
                "participantId": "Participant_Requester"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_CreateRequisition",
            "parentId": "Process_Requester",
            "name": "Create Purchase Requisition",
            "data": {
                "label": "Create detailed purchase requisition",
                "participantId": "Participant_Requester"
            }
        },
        {
            "type": "bpmn:intermediateThrowEvent",
            "id": "Event_SubmitRequisition",
            "parentId": "Process_Requester",
            "name": "Submit to Procurement",
            "data": {
                "label": "Send requisition to procurement team",
                "participantId": "Participant_Requester",
                "eventDefinition": {
                    "type": "bpmn:messageEventDefinition"
                }
            }
        },
        {
            "type": "bpmn:intermediateCatchEvent",
            "id": "Event_GoodsReceived",
            "parentId": "Process_Requester",
            "name": "Goods Received",
            "data": {
                "label": "Receive goods from vendor",
                "participantId": "Participant_Requester",
                "eventDefinition": {
                    "type": "bpmn:messageEventDefinition"
                }
            }
        },
        {
            "type": "bpmn:intermediateThrowEvent",
            "id": "Event_SendReceipt",
            "parentId": "Process_Requester",
            "name": "Send Receipt to Finance",
            "data": {
                "label": "Send goods receipt confirmation to finance",
                "participantId": "Participant_Requester",
                "eventDefinition": {
                    "type": "bpmn:messageEventDefinition"
                }
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_RequesterComplete",
            "parentId": "Process_Requester",
            "name": "Procurement Complete",
            "data": {
                "label": "Procurement process completed for requester",
                "participantId": "Participant_Requester"
            }
        },
        {
            "type": "bpmn:messageFlow",
            "id": "MessageFlow_RequisitionToProcurement",
            "source": "Event_SubmitRequisition",
            "target": "StartEvent_ProcurementReceive",
            "name": "Purchase Requisition"
        },
        {
            "type": "bpmn:messageFlow",
            "id": "MessageFlow_POToVendor",
            "source": "Event_SendPO",
            "target": "StartEvent_VendorReceive",
            "name": "Purchase Order"
        },
        {
            "type": "bpmn:messageFlow",
            "id": "MessageFlow_GoodsToRequester",
            "source": "Event_DeliverGoods",
            "target": "Event_GoodsReceived",
            "name": "Goods Delivery"
        },
        {
            "type": "bpmn:messageFlow",
            "id": "MessageFlow_InvoiceToFinance",
            "source": "Event_SendInvoice",
            "target": "StartEvent_FinanceInvoice",
            "name": "Invoice"
        },
        {
            "type": "bpmn:messageFlow",
            "id": "MessageFlow_ReceiptToFinance",
            "source": "Event_SendReceipt",
            "target": "Event_ReceiveReceipt",
            "name": "Goods Receipt"
        }
    ]
}
```

## Example 4: Visio Diagram - Insurance Claim with Comprehensive Elements

**Document Type:** Visio Diagram (.vsdx)

**Extracted Content:**
```
Insurance Claim Processing (From Visio)
Shapes and Connections:
- Start: "Claim Submitted"
- Process: "Initial Review"
- Decision: "Complete Documentation?"
- Process: "Request Additional Documents"
- Subprocess: "Investigation Process"
  - Sub-start: "Begin Investigation"
  - Sub-process: "Gather Evidence"
  - Sub-process: "Interview Parties"
  - Sub-process: "Assess Damages"
  - Sub-decision: "Fraud Suspected?"
  - Sub-process: "Fraud Investigation"
  - Sub-end: "Investigation Complete"
- Decision: "Approve Claim?"
- Process: "Calculate Settlement"
- Process: "Issue Payment"
- Process: "Send Denial Letter"
- End: "Claim Processed"
- End: "Claim Denied"
```

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Comprehensive insurance claim process from Visio diagram showing subprocess with internal decision logic, multiple end events, and proper element variety including lanes and data objects.",
    "title": "Insurance Claim Processing",
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_InsuranceClaim",
            "name": "Insurance Claim Processing"
        },
        {
            "type": "bpmn:laneSet",
            "id": "LaneSet_Claims",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:lane",
            "id": "Lane_ClaimsAgent",
            "parentId": "LaneSet_Claims",
            "name": "Claims Agent"
        },
        {
            "type": "bpmn:lane",
            "id": "Lane_Investigator",
            "parentId": "LaneSet_Claims",
            "name": "Investigator"
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_ClaimSubmitted",
            "parentId": "Process_InsuranceClaim",
            "name": "Claim Submitted",
            "data": {
                "label": "Customer submits insurance claim",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_InitialReview",
            "parentId": "Process_InsuranceClaim",
            "name": "Initial Review",
            "data": {
                "label": "Claims agent performs initial review",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_DocumentationComplete",
            "parentId": "Process_InsuranceClaim",
            "name": "Complete Documentation?",
            "data": {
                "label": "Is claim documentation complete?",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_RequestDocuments",
            "parentId": "Process_InsuranceClaim",
            "name": "Request Additional Documents",
            "data": {
                "label": "Request missing documentation from claimant",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:subProcess",
            "id": "SubProcess_Investigation",
            "parentId": "Process_InsuranceClaim",
            "name": "Investigation Process",
            "data": {
                "label": "Detailed claim investigation subprocess",
                "laneId": "Lane_Investigator",
                "isExpanded": true
            }
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_Investigation",
            "parentId": "SubProcess_Investigation",
            "name": "Begin Investigation"
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_GatherEvidence",
            "parentId": "SubProcess_Investigation",
            "name": "Gather Evidence",
            "data": {
                "label": "Collect and document evidence related to claim"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_InterviewParties",
            "parentId": "SubProcess_Investigation",
            "name": "Interview Parties",
            "data": {
                "label": "Interview claimant and witnesses"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_FraudSuspected",
            "parentId": "SubProcess_Investigation",
            "name": "Fraud Suspected?",
            "data": {
                "label": "Is fraud suspected based on evidence?"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_FraudInvestigation",
            "parentId": "SubProcess_Investigation",
            "name": "Fraud Investigation",
            "data": {
                "label": "Conduct detailed fraud investigation"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_AssessDamages",
            "parentId": "SubProcess_Investigation",
            "name": "Assess Damages",
            "data": {
                "label": "Evaluate and document claim damages"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_Investigation",
            "parentId": "SubProcess_Investigation",
            "name": "Investigation Complete"
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_ApproveClaim",
            "parentId": "Process_InsuranceClaim",
            "name": "Approve Claim?",
            "data": {
                "label": "Should claim be approved based on investigation?",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:businessRuleTask",
            "id": "Task_CalculateSettlement",
            "parentId": "Process_InsuranceClaim",
            "name": "Calculate Settlement",
            "data": {
                "label": "Calculate settlement amount using business rules",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:serviceTask",
            "id": "Task_IssuePayment",
            "parentId": "Process_InsuranceClaim",
            "name": "Issue Payment",
            "data": {
                "label": "Process payment to claimant",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:sendTask",
            "id": "Task_SendDenialLetter",
            "parentId": "Process_InsuranceClaim",
            "name": "Send Denial Letter",
            "data": {
                "label": "Send claim denial notification",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_ClaimProcessed",
            "parentId": "Process_InsuranceClaim",
            "name": "Claim Processed",
            "data": {
                "label": "Claim successfully processed and paid",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_ClaimDenied",
            "parentId": "Process_InsuranceClaim",
            "name": "Claim Denied",
            "data": {
                "label": "Claim denied and notification sent",
                "laneId": "Lane_ClaimsAgent"
            }
        },
        {
            "type": "bpmn:dataObjectReference",
            "id": "DataObject_ClaimForm",
            "parentId": "Process_InsuranceClaim",
            "name": "Claim Form",
            "data": {
                "label": "Original claim submission form"
            }
        },
        {
            "type": "bpmn:dataObjectReference",
            "id": "DataObject_InvestigationReport",
            "parentId": "Process_InsuranceClaim",
            "name": "Investigation Report",
            "data": {
                "label": "Detailed investigation findings"
            }
        },
        {
            "type": "bpmn:textAnnotation",
            "id": "Annotation_Process",
            "parentId": "Process_InsuranceClaim",
            "name": "Process Note",
            "data": {
                "label": "Standard claim processing follows regulatory guidelines"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Start_Review",
            "source": "StartEvent_ClaimSubmitted",
            "target": "Task_InitialReview",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Review_Documentation",
            "source": "Task_InitialReview",
            "target": "Gateway_DocumentationComplete",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Incomplete_Request",
            "source": "Gateway_DocumentationComplete",
            "target": "Task_RequestDocuments",
            "parentId": "Process_InsuranceClaim",
            "name": "Incomplete",
            "data": {
                "label": "Documentation is incomplete"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Request_Review",
            "source": "Task_RequestDocuments",
            "target": "Task_InitialReview",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Complete_Investigation",
            "source": "Gateway_DocumentationComplete",
            "target": "SubProcess_Investigation",
            "parentId": "Process_InsuranceClaim",
            "name": "Complete",
            "data": {
                "label": "Documentation is complete"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Investigation_Approval",
            "source": "SubProcess_Investigation",
            "target": "Gateway_ApproveClaim",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Approve_Calculate",
            "source": "Gateway_ApproveClaim",
            "target": "Task_CalculateSettlement",
            "parentId": "Process_InsuranceClaim",
            "name": "Approve",
            "data": {
                "label": "Claim approved for payment"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Deny_Letter",
            "source": "Gateway_ApproveClaim",
            "target": "Task_SendDenialLetter",
            "parentId": "Process_InsuranceClaim",
            "name": "Deny",
            "data": {
                "label": "Claim denied"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Calculate_Payment",
            "source": "Task_CalculateSettlement",
            "target": "Task_IssuePayment",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Payment_Processed",
            "source": "Task_IssuePayment",
            "target": "EndEvent_ClaimProcessed",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Letter_Denied",
            "source": "Task_SendDenialLetter",
            "target": "EndEvent_ClaimDenied",
            "parentId": "Process_InsuranceClaim"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Sub_Start_Evidence",
            "source": "StartEvent_Investigation",
            "target": "Task_GatherEvidence",
            "parentId": "SubProcess_Investigation"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Evidence_Interview",
            "source": "Task_GatherEvidence",
            "target": "Task_InterviewParties",
            "parentId": "SubProcess_Investigation"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Interview_Fraud",
            "source": "Task_InterviewParties",
            "target": "Gateway_FraudSuspected",
            "parentId": "SubProcess_Investigation"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Fraud_Investigation",
            "source": "Gateway_FraudSuspected",
            "target": "Task_FraudInvestigation",
            "parentId": "SubProcess_Investigation",
            "name": "Yes",
            "data": {
                "label": "Fraud suspected, conduct detailed investigation"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_NoFraud_Assess",
            "source": "Gateway_FraudSuspected",
            "target": "Task_AssessDamages",
            "parentId": "SubProcess_Investigation",
            "name": "No",
            "data": {
                "label": "No fraud suspected, proceed to assessment"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_FraudDone_Assess",
            "source": "Task_FraudInvestigation",
            "target": "Task_AssessDamages",
            "parentId": "SubProcess_Investigation"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Assess_End",
            "source": "Task_AssessDamages",
            "target": "EndEvent_Investigation",
            "parentId": "SubProcess_Investigation"
        }
    ]
}
```

## Example 5: Image Processing - Manufacturing Quality Control

**Image Description:**
A flowchart image showing a manufacturing quality control process with parallel inspection paths and decision points.

**Visual Elements Identified:**
- Start symbol: "Product Received"
- Process box: "Initial Inspection" 
- Decision diamond: "Meets Standards?"
- Parallel fork: Two paths for "Visual Inspection" and "Measurement Testing"
- Process boxes: "Document Issues", "Approve for Shipping"
- Decision diamond: "Rework Possible?"
- End symbols: "Ship Product", "Scrap Product"

**Expected BPMN Model in JSON Format:**
```json
{
    "explanation": "Manufacturing quality control process from flowchart image showing parallel inspection paths, multiple decision points, and proper gateway usage for complex routing logic.",
    "title": "Manufacturing Quality Control",
    "add": [
        {
            "type": "bpmn:process",
            "id": "Process_QualityControl",
            "name": "Manufacturing Quality Control"
        },
        {
            "type": "bpmn:startEvent",
            "id": "StartEvent_ProductReceived",
            "parentId": "Process_QualityControl",
            "name": "Product Received",
            "data": {
                "label": "Product arrives at quality control station"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_InitialInspection",
            "parentId": "Process_QualityControl",
            "name": "Initial Inspection",
            "data": {
                "label": "Perform initial visual and basic inspection"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_MeetsStandards",
            "parentId": "Process_QualityControl",
            "name": "Meets Standards?",
            "data": {
                "label": "Does product meet initial quality standards?"
            }
        },
        {
            "type": "bpmn:parallelGateway",
            "id": "Gateway_ParallelInspection",
            "parentId": "Process_QualityControl",
            "name": "Split Inspection",
            "data": {
                "label": "Split into parallel inspection paths"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_VisualInspection",
            "parentId": "Process_QualityControl",
            "name": "Visual Inspection",
            "data": {
                "label": "Detailed visual inspection for defects"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_MeasurementTesting",
            "parentId": "Process_QualityControl",
            "name": "Measurement Testing",
            "data": {
                "label": "Precision measurement and dimensional testing"
            }
        },
        {
            "type": "bpmn:parallelGateway",
            "id": "Gateway_JoinInspection",
            "parentId": "Process_QualityControl",
            "name": "Join Inspection",
            "data": {
                "label": "Merge parallel inspection results"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_FinalApproval",
            "parentId": "Process_QualityControl",
            "name": "Final Approval",
            "data": {
                "label": "Final approval decision based on all inspections"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_DocumentIssues",
            "parentId": "Process_QualityControl",
            "name": "Document Issues",
            "data": {
                "label": "Document quality issues and defects found"
            }
        },
        {
            "type": "bpmn:exclusiveGateway",
            "id": "Gateway_ReworkPossible",
            "parentId": "Process_QualityControl",
            "name": "Rework Possible?",
            "data": {
                "label": "Can defects be fixed through rework?"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ReworkProduct",
            "parentId": "Process_QualityControl",
            "name": "Rework Product",
            "data": {
                "label": "Perform necessary rework to fix defects"
            }
        },
        {
            "type": "bpmn:userTask",
            "id": "Task_ApproveShipping",
            "parentId": "Process_QualityControl",
            "name": "Approve for Shipping",
            "data": {
                "label": "Approve product for shipment to customer"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_ShipProduct",
            "parentId": "Process_QualityControl",
            "name": "Ship Product",
            "data": {
                "label": "Product approved and shipped"
            }
        },
        {
            "type": "bpmn:endEvent",
            "id": "EndEvent_ScrapProduct",
            "parentId": "Process_QualityControl",
            "name": "Scrap Product",
            "data": {
                "label": "Product scrapped due to irreparable defects"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Start_Initial",
            "source": "StartEvent_ProductReceived",
            "target": "Task_InitialInspection",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Initial_Standards",
            "source": "Task_InitialInspection",
            "target": "Gateway_MeetsStandards",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Standards_Parallel",
            "source": "Gateway_MeetsStandards",
            "target": "Gateway_ParallelInspection",
            "parentId": "Process_QualityControl",
            "name": "Meets Standards",
            "data": {
                "label": "Product meets initial standards, proceed with detailed inspection"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Standards_Document",
            "source": "Gateway_MeetsStandards",
            "target": "Task_DocumentIssues",
            "parentId": "Process_QualityControl",
            "name": "Fails Standards",
            "data": {
                "label": "Product fails initial standards"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Parallel_Visual",
            "source": "Gateway_ParallelInspection",
            "target": "Task_VisualInspection",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Parallel_Measurement",
            "source": "Gateway_ParallelInspection",
            "target": "Task_MeasurementTesting",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Visual_Join",
            "source": "Task_VisualInspection",
            "target": "Gateway_JoinInspection",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Measurement_Join",
            "source": "Task_MeasurementTesting",
            "target": "Gateway_JoinInspection",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Join_Approval",
            "source": "Gateway_JoinInspection",
            "target": "Gateway_FinalApproval",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Approval_Ship",
            "source": "Gateway_FinalApproval",
            "target": "Task_ApproveShipping",
            "parentId": "Process_QualityControl",
            "name": "Approved",
            "data": {
                "label": "Product passes all quality checks"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Approval_Document",
            "source": "Gateway_FinalApproval",
            "target": "Task_DocumentIssues",
            "parentId": "Process_QualityControl",
            "name": "Rejected",
            "data": {
                "label": "Product fails quality checks"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Ship_End",
            "source": "Task_ApproveShipping",
            "target": "EndEvent_ShipProduct",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Document_Rework",
            "source": "Task_DocumentIssues",
            "target": "Gateway_ReworkPossible",
            "parentId": "Process_QualityControl"
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Rework_Yes",
            "source": "Gateway_ReworkPossible",
            "target": "Task_ReworkProduct",
            "parentId": "Process_QualityControl",
            "name": "Yes",
            "data": {
                "label": "Defects can be repaired"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Rework_No",
            "source": "Gateway_ReworkPossible",
            "target": "EndEvent_ScrapProduct",
            "parentId": "Process_QualityControl",
            "name": "No",
            "data": {
                "label": "Defects cannot be repaired"
            }
        },
        {
            "type": "bpmn:sequenceFlow",
            "id": "Flow_Rework_Initial",
            "source": "Task_ReworkProduct",
            "target": "Task_InitialInspection",
            "parentId": "Process_QualityControl"
        }
    ]
}
``` 