from pydantic import TypeAdapter

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    BpmnElementTypes,
    ExtensionTypes,
    ImplementationTypes,
    ResourceKinds,
    ResourceTypes,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    ActionApp,
    AgentExtensionInfo,
    BpmnElement,
    BpmnExtensionPlan,
    BpmnRequestContext,
    Connector,
    ConnectorObject,
    ElementExtension,
    ElementExtensionItem,
    ElementImplementationResult,
    ExtensionData,
    ExtensionInfo,
    ExtensionInputRequest,
    ExtensionType,
    Process,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.connector_service import ConnectorService, ObjectWithEmbedding
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_base_task import BpmnExtensionBaseTask
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_input_task import BpmnExtensionInputTask
from services.studio._text_to_workflow.bpmn_generation.fps_client import get_solution_resources
from services.studio._text_to_workflow.models.model_manager import ModelManager
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger, log_execution_time

LOGGER = AppInsightsLogger()
CONNECTOR_SERVICE: ConnectorService = ConnectorService()
EXTENSION_INPUT_TASK = BpmnExtensionInputTask()


class AgentBpmnElement(BpmnElement):
    external_agent: bool | None = None

    def __init__(self, element: BpmnElement, external_agent: bool | None = None):
        super().__init__(**element.model_dump())
        self.external_agent = external_agent


class BpmnExtensionAgentTask(BpmnExtensionBaseTask):
    def __init__(self):
        super().__init__()
        self.embedding_model = ModelManager().get_embeddings_model("bpmn_embedding_model")

    @log_execution_time("bpmn_extension_agent_task.generate")
    async def generate(
        self,
        context: BpmnRequestContext,
        plans: list[BpmnExtensionPlan],
        connectors: list[Connector],
        external_agents: list[Process],
        model_name: str,
        chat_history: str,
    ) -> list[ElementExtension]:
        candidates, agents, external_agents_by_fake_id = await self._gather_resources(context, external_agents)
        elements: list[AgentBpmnElement] = [AgentBpmnElement(plan.element, external_agent=plan.external_agent) for plan in plans]

        prompts = self.config["prompt"]
        system_message = self._system_template(prompts["system"])
        user_message = self._human_template(prompts["user"]).format(
            elements=TypeAdapter(list[AgentBpmnElement]).dump_json(elements).decode("utf-8"),
            implementation_type=ImplementationTypes.AGENT,
            implementations=TypeAdapter(list[AgentExtensionInfo]).dump_json(candidates, exclude_none=True).decode("utf-8"),
            user_query=context.user_request or "",
            chat_history=chat_history,
        )

        result, _ = await self._call_llm(system_message, user_message, use_case=model_name)
        # LOGGER.info(f"Result of extension suggestion is : [{result}]")
        results: dict[str, ElementImplementationResult] = TypeAdapter(dict[str, ElementImplementationResult]).validate_json(
            result.strip("```json\n").strip("\n```")
        )

        return await self._process_extensions(context, results, elements, agents, external_agents_by_fake_id)

    @log_execution_time("bpmn_extension_agent_task.gather_resources")
    async def _gather_resources(
        self,
        context: BpmnRequestContext,
        external_agents: list[Process],
    ) -> tuple[list[ExtensionInfo], dict[int, SolutionResource], dict[int, Process]]:
        """Gather resources for BPMN extension from APIs."""
        request_context = context.request_context
        extension_data = context.extension_data_override
        if extension_data:
            agents = [
                SolutionResource(
                    fakeId=process.fakeId,
                    name=process.name,
                    score=process.score,
                    description=process.description,
                    id=process.id,
                    type=process.type,
                    kind=ResourceKinds.PROCESS,
                    folder=None,
                    inputJson=None,
                    resourceType=None,
                )
                for process in extension_data.processes or []
                if process.type == ExtensionTypes.AGENT
            ]
        else:
            agents = await get_solution_resources(request_context, context.solution_id, context.project_key, ResourceKinds.PROCESS, [ResourceTypes.AGENT])

        # Build simpler data structure for processes and agents and fake integer id for reducing token size
        fake_id = 1
        agents_by_fake_id: dict[int, SolutionResource] = {}
        candidates: list[ExtensionInfo] = []
        for agent in agents:
            agent.fakeId = fake_id
            candidates.append(AgentExtensionInfo(id=fake_id, name=agent.name, description=agent.description, score=None, external=False))
            agents_by_fake_id[agent.fakeId] = agent
            fake_id += 1

        external_agents_by_fake_id: dict[int, Process] = {}
        for external_agent in external_agents:
            external_agent.fakeId = fake_id
            candidates.append(AgentExtensionInfo(id=fake_id, name=external_agent.name, description=external_agent.description, score=None, external=True))
            external_agents_by_fake_id[external_agent.fakeId] = external_agent
            fake_id += 1

        return candidates, agents_by_fake_id, external_agents_by_fake_id

    async def _process_extensions(
        self,
        context: BpmnRequestContext,
        results: dict[str, ElementImplementationResult],
        elements: list[AgentBpmnElement],
        agents: dict[int, SolutionResource],
        external_agents: dict[int, Process],
    ) -> list[ElementExtension]:
        extension_results: list[ElementExtension] = []
        request: ExtensionInputRequest = ExtensionInputRequest(extension_type=ExtensionType.AGENT, items=[])
        for element in elements:
            if element.id not in results:
                LOGGER.error(f"LLM generated hallucinated element id for element [{element.id}]")
                continue

            element_extension = results[element.id]
            if element_extension.error:
                LOGGER.error(f"Error in generating extension for element [{element.id}]: {element_extension.error}")
                continue

            query_embedding = self.embedding_model.encode(f"{element.purpose}", instruction_set="icl", instruction_type="query")
            extensions: list[Process | ActionApp | Connector | SolutionResource] = []
            suggestions: list[Connector | SolutionResource] = []
            for suggestion in element_extension.suggestions or []:
                if (suggestion.id not in agents) and (suggestion.id not in external_agents):
                    LOGGER.error(f"LLM generated hallucinated suggestion id for element [{element.id}]")
                    continue
                agent = external_agents.get(suggestion.id)
                if agent:
                    solutions = await get_solution_resources(
                        context.request_context,
                        context.solution_id,
                        context.project_key,
                        ResourceKinds.CONNECTION,
                        [agent.id],
                    )
                    if solutions and len(solutions) > 0:
                        agent.connection = await CONNECTOR_SERVICE._get_connection(agent.id, context, solutions[0].name)
                        if agent.connection:
                            object: ObjectWithEmbedding | None = await CONNECTOR_SERVICE._get_object(agent.id, context, query_embedding)
                            if object:
                                execution_type = object["executionType"]
                                agent.type = f"Intsvc.{execution_type.capitalize()}AgentExecution"
                                agent.activity = ConnectorObject(name=object["name"], displayName=object["displayName"], executionType=execution_type)
                else:
                    agent = agents[suggestion.id]
                    suggestions.append(agent)

                agent.score = suggestion.score or 0
                extensions.append(agent)
                if len(suggestions) > 0:
                    request.items.append(ElementExtensionItem(element=element, suggestions=suggestions))

            extension_results.append(ElementExtension(id=element.id, type=BpmnElementTypes.SERVICE, data=ExtensionData(suggestions=extensions)))

        if len(request.items) > 0:
            await EXTENSION_INPUT_TASK.extension_input_generate(context, request)
        return extension_results
