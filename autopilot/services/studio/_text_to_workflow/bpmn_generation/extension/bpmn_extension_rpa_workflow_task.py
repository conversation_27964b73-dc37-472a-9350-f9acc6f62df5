from typing_extensions import override

from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    BpmnElementTypes,
    ExtensionTypes,
    ImplementationTypes,
    ResourceKinds,
    ResourceTypes,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnExtensionPlan,
    ExtensionDataOverride,
    SolutionResource,
)
from services.studio._text_to_workflow.bpmn_generation.extension.bpmn_extension_solution_resource_task import BpmnExtensionSolutionResourceTask
from services.studio._text_to_workflow.utils.telemetry_utils import AppInsightsLogger

LOGGER = AppInsightsLogger()


class BpmnExtensionRpaWorkflowTask(BpmnExtensionSolutionResourceTask):
    def __init__(self):
        super().__init__(ImplementationTypes.PROCESS, ResourceKinds.PROCESS, [ResourceTypes.PROCESS])

    @override
    def _get_task_type(self, plan: BpmnExtensionPlan) -> str:
        return BpmnElementTypes.SERVICE

    @override
    def _get_extension_type(self, plan: BpmnExtensionPlan) -> str:
        return ExtensionTypes.PROCESS

    @override
    def _get_eval_dataset(self, extension_data_override: ExtensionDataOverride) -> list[SolutionResource]:
        return [
            SolutionResource(
                fakeId=process.fakeId,
                name=process.name,
                score=process.score,
                description=process.description,
                id=process.id,
                type=process.type,
                kind=ResourceKinds.PROCESS,
                folder=None,
                inputJson=None,
                resourceType=None,
            )
            for process in extension_data_override.processes or []
            if process.type == ExtensionTypes.PROCESS
        ]
