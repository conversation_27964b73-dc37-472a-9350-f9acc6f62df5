import json
import os
import pathlib
import re
from typing import Literal, TypedDict

from fastapi.encoders import jsonable_encoder
from langchain.schema import HumanMessage, SystemMessage
from langchain_core.runnables.config import RunnableConfig
from langgraph.graph import END, START, StateGraph
from langgraph.graph.graph import CompiledGraph

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    CANNOT_PROCESS_REQUEST_EXPLANATION,
    DIRECT_UPLOAD_MIME_TYPES,
    MIME_TO_READABLE_NAME,
    TEXT_EXTRACTION_MIME_TYPES,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_from_document_task import (
    BpmnGenerationFromDocumentTask,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationToolResult,
    BpmnRequestContext,
    ChatHistory,
    LightModelType,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import (
    BpmnValidator,
)
from services.studio._text_to_workflow.bpmn_generation.business_process_agent_schema import BusinessProcessAgentState
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

VALIDATOR = BpmnValidator()
LOGGER = AppInsightsLogger()

BPMN_PARSER_PROMPT = "prompt_parser.yaml"
BPMN_TRANSLATOR_PROMPT = "prompt_translator.yaml"
BPMN_SUPPORTED_ELEMENTS = "bpmn_elements.yaml"
BPMN_IMAGE_PARSER_PROMPT = "image_parser.yaml"
BPMN_DOCUMENT_PARSER_PROMPT = "document_parser.yaml"

BPMN_PATCH_SCHEMA = BpmnBaseTask.read_content(pathlib.Path(os.path.join(os.path.join(os.path.dirname(__file__), "config"), "bpmn_patch_schema.json")))


class AgentState(TypedDict):
    user_request: str
    current_bpmn: str
    plan: str
    planner_model_name: str
    translator_model_name: str
    translated_bpmn: str
    chat_history: dict[Tool, list[ChatHistory]] | None
    relevant_elements: list[str] | None
    image_data: bytes | None
    document_data: bytes | None
    usage_token: list[TokenUsage]
    tool: Tool


def yaml_load_from_edit_config(filename: str):
    return yaml_load(pathlib.Path(os.path.join(os.path.dirname(__file__), "config", "bpmn_edit_flow", filename)))


class BpmnGenerationFlowTask(BpmnBaseTask):
    def __init__(self) -> None:
        self.parser_prompt = yaml_load_from_edit_config(BPMN_PARSER_PROMPT)
        self.translator_prompt = yaml_load_from_edit_config(BPMN_TRANSLATOR_PROMPT)
        self.image_parser_prompt = yaml_load_from_edit_config(BPMN_IMAGE_PARSER_PROMPT)
        self.document_parser_prompt = yaml_load_from_edit_config(BPMN_DOCUMENT_PARSER_PROMPT)
        self.bpmn_elements = yaml_load_from_edit_config(BPMN_SUPPORTED_ELEMENTS)
        super().__init__("generation_prompt.yaml")

    def get_relevant_element_definitions(self, state: AgentState) -> str:
        relevant_elements = state["relevant_elements"]
        if relevant_elements is None or len(relevant_elements) == 0:
            LOGGER.warning("No relevant elements found in the response.")
            return ""
        all_element_defs = self.bpmn_elements["elements"]
        relevant_element_defs = {}
        for element in relevant_elements:
            if element not in all_element_defs:
                LOGGER.warning(f"Relevant element '{element}' is not in the element definition list.")
                continue
            def_str = all_element_defs[element]
            relevant_element_defs[element] = def_str

        if len(relevant_element_defs) != len(relevant_elements):
            LOGGER.warning(
                f"Some relevant elements were not found in the element definition list, got definition {len(relevant_element_defs)} out of {len(relevant_elements)}."
            )

        if len(relevant_element_defs) == 0:
            LOGGER.warning("No relevant elements found in the examples.")
            return ""
        relevant_elements_str = "\n".join([f"- {ele_key}\n{def_str}" for ele_key, def_str in relevant_element_defs.items()])
        return relevant_elements_str

    @log_execution_time("bpmn_generation_task_flow.generate.request_to_plan")
    async def text_planner_agent(self, state: AgentState) -> dict:
        related_tools = [Tool.EDIT_BPMN, Tool.QA, Tool.EXTENSION]
        chat_history_str = (
            await self._get_related_chat_history(state["chat_history"], related_tools, state["planner_model_name"]) if state["chat_history"] else "none"
        )

        system_prompt_template = self.parser_prompt["prompt"]["system"]
        formatted_system_prompt = system_prompt_template.format(
            input_schema=self.parser_prompt["prompt"]["input_schema"],
            output_schema=self.parser_prompt["prompt"]["output_schema"],
            element_types=self.bpmn_elements["element_types"],
            parsing_examples=self.parser_prompt["prompt"]["parsing_examples"],
        )

        user_request = state["user_request"]
        if "User: pending" in chat_history_str.strip():
            user_request += " Also integrate the latest pending change for edit after the last accepted or declined one."
        user_content = f"userRequest: {user_request}\ncurrentBpmn: {state['current_bpmn']}\nchatHistory: {chat_history_str}"

        messages = [
            SystemMessage(content=formatted_system_prompt),
            HumanMessage(content=user_content),
        ]

        response, token_usage = await self._call_llm(messages[0], messages[1], state["planner_model_name"])

        response_obj = json.loads(response.strip("```json\n").strip("\n```"))

        return {
            "plan": {"plan": response_obj["plan"], "explanation": response_obj["explanation"]},
            "relevant_elements": response_obj["relevant_elements"],
            "usage_token": state.get("usage_token") + [token_usage],
            "tool": Tool.EDIT_BPMN,
        }

    @log_execution_time("bpmn_generation_task_flow.generate.plan_to_json")
    async def translator_agent(self, state: AgentState) -> dict:
        all_element_defs = self.get_relevant_element_definitions(state)
        messages = [
            SystemMessage(
                content=self.translator_prompt["prompt"]["system"].format(
                    all_element_definitions=all_element_defs,
                    bpmn_examples=self.translator_prompt["prompt"]["translation_examples"],
                    input_schema=self.translator_prompt["prompt"]["input_schema"],
                    output_schema=self.translator_prompt["prompt"]["output_schema"],
                )
            ),
            HumanMessage(content=f"Plan: {state['plan']}\nCurrent BPMN: {state['current_bpmn']}"),
        ]

        response, token_usage = await self._call_llm(messages[0], messages[1], state["translator_model_name"])

        return {
            "translated_bpmn": response.strip("```json\n").strip("\n```"),
            "usage_token": state.get("usage_token") + [token_usage],
        }

    @log_execution_time("bpmn_generation_task_flow.generate.image_to_plan")
    async def image_planner_agent(self, state: AgentState) -> dict:
        # Use standard query for consistency
        standard_query = "Convert this image to a proper BPMN edit plan following BPMN 2.0 standard."
        LOGGER.info(message=f"Using standard image conversion prompt: {standard_query}.")

        # Handle potential image format errors
        if state["image_data"]:
            image_base64 = state["image_data"].decode("utf-8")
            mime_type, error = self._get_file_mime_type(image_base64)
            if error:
                LOGGER.error(f"Bad request: {error}")
                return {
                    "plan": {"plan": {"add": [], "update": [], "delete": []}, "explanation": f"The provided image format is not supported: {error}"},
                    "relevant_elements": [],
                }

            system_message = self._system_template(self.image_parser_prompt["prompt"]["system"]).format(
                output_schema=self.image_parser_prompt["prompt"]["output_schema"],
                element_types=self.bpmn_elements["element_types"],
            )

            user_message = HumanMessage(
                content=[
                    {
                        "type": "text",
                        "text": standard_query,
                    },
                    {
                        "type": mime_type,
                        "image_url": {"url": f"{image_base64}"},
                    },
                ]
            )

            response, token_usage = await self._call_llm(system_message, user_message, state["planner_model_name"])

            response_obj = json.loads(response.strip("```json\n").strip("\n```"))

            process_id_to_delete = self.extract_process_id(state["current_bpmn"])
            plan = response_obj["plan"]
            if process_id_to_delete:
                plan["delete"] = [{"id": process_id_to_delete, "description": "Remove current process"}]
            return {
                "plan": {"plan": plan, "explanation": response_obj["explanation"]},
                "relevant_elements": response_obj["relevant_elements"],
                "usage_token": state.get("usage_token", []) + [token_usage],
                "tool": Tool.CONVERT_IMAGE,
            }

        # no image data is provided
        LOGGER.warning("No image data found in state for image_planner_agent.")
        return {
            "plan": {"plan": {"add": [], "update": [], "delete": []}, "explanation": "No image data provided to generate a plan."},
            "relevant_elements": [],
        }

    @log_execution_time("bpmn_generation_task_flow.generate.document_to_plan")
    async def document_planner_agent(self, state: AgentState) -> dict:
        # Use standard query for consistency
        standard_query = "Convert this document to a proper BPMN edit plan following BPMN 2.0 standard."
        LOGGER.info(f"Using standard document conversion prompt: {standard_query}")

        if not state.get("document_data"):
            LOGGER.warning("No document data found in state for document_planner_agent.")
            return {
                "plan": {"plan": {"add": [], "update": [], "delete": []}, "explanation": "No document data provided to generate a plan."},
                "relevant_elements": [],
            }

        try:
            document_task = BpmnGenerationFromDocumentTask()
            document_base64 = state["document_data"].decode("utf-8")  # type: ignore

            # Detect file type using document task
            mime_type = document_task._detect_and_validate_file(document_base64)
            readable_type = MIME_TO_READABLE_NAME.get(mime_type, mime_type)
            LOGGER.info(f"Detected document type: {readable_type} ({mime_type})")

            # Use document parser system message (similar to image planner)
            system_message = self._system_template(self.document_parser_prompt["prompt"]["system"]).format(
                output_schema=self.document_parser_prompt["prompt"]["output_schema"],
                element_types=self.bpmn_elements["element_types"],
            )

            if mime_type in DIRECT_UPLOAD_MIME_TYPES:
                # Handle direct upload files (PDFs, images) - send directly to LLM
                LOGGER.info(f"Processing {readable_type} for direct upload")
                content_item = document_task._prepare_direct_upload_content(document_base64, mime_type)
                user_message = HumanMessage(
                    content=[
                        {
                            "type": "text",
                            "text": f"{standard_query}\n\nContent Type: {readable_type}\nAnalyze this content for BPMN elements, flowcharts, or process diagrams.",
                        },
                        content_item,
                    ]
                )

            elif mime_type in TEXT_EXTRACTION_MIME_TYPES:
                # Handle structured documents - extract text only (following document task approach)
                LOGGER.info(f"Processing {readable_type} document with text extraction")
                extracted_text = await document_task._process_office_document(document_base64, mime_type)
                LOGGER.info(f"Extracted {len(extracted_text)} characters from {readable_type}")

                # Use text-only approach (consistent with document task implementation)
                user_message = HumanMessage(
                    content=f"{standard_query}\n\nContent Type: {readable_type} Document\nAnalyze the extracted text content to identify business processes and create BPMN elements.\n\n## EXTRACTED TEXT CONTENT:\n{extracted_text}"
                )

            else:
                # Unsupported file type
                LOGGER.error(f"Unsupported document type: {readable_type} ({mime_type})")
                return {
                    "plan": {"plan": {"add": [], "update": [], "delete": []}, "explanation": f"Unsupported document type: {readable_type}"},
                    "relevant_elements": [],
                }

            # Call LLM with content (same pattern as image planner)
            response, token_usage = await self._call_llm(system_message, user_message, state["planner_model_name"])
            response_obj = json.loads(response.strip("```json\n").strip("\n```"))

            # Handle process deletion (same pattern as image planner)
            process_id_to_delete = self.extract_process_id(state["current_bpmn"])
            plan = response_obj["plan"]
            if process_id_to_delete:
                plan["delete"] = [{"id": process_id_to_delete, "description": "Remove current process"}]

            LOGGER.info(f"Successfully generated plan from {readable_type} document")
            return {
                "plan": {"plan": plan, "explanation": response_obj["explanation"]},
                "relevant_elements": response_obj["relevant_elements"],
                "usage_token": state.get("usage_token", []) + [token_usage],
                "tool": Tool.CONVERT_DOCUMENT,
            }

        except Exception as e:
            LOGGER.error(f"Error processing document: {str(e)}")
            return {
                "plan": {"plan": {"add": [], "update": [], "delete": []}, "explanation": f"Error processing document: {str(e)}"},
                "relevant_elements": [],
            }

    def route_to_planner(self, state: AgentState) -> Literal["image", "document", "text"]:
        # Route based on request parameter type:
        # - document_data (from 'document' param) -> document planner (Tool.CONVERT_DOCUMENT)
        # - image_data (from 'image' param) -> image planner (Tool.CONVERT_IMAGE - legacy path)
        # - neither -> text planner

        if state.get("document_data"):
            return "document"  # New unified document planner (handles PDFs, DOCs, and images)
        elif state.get("image_data"):
            return "image"
        else:
            return "text"

    def _build_agent_graph(self) -> CompiledGraph:
        builder = StateGraph(AgentState)

        builder.add_conditional_edges(
            START,
            self.route_to_planner,
            {
                "text": "text_planner",
                "image": "image_planner",
                "document": "document_planner",
            },
        )
        builder.add_node("text_planner", self.text_planner_agent)
        builder.add_node("image_planner", self.image_planner_agent)
        builder.add_node("document_planner", self.document_planner_agent)
        builder.add_node("plan_to_json_translator", self.translator_agent)

        builder.add_edge("image_planner", "plan_to_json_translator")
        builder.add_edge("text_planner", "plan_to_json_translator")
        builder.add_edge("document_planner", "plan_to_json_translator")
        builder.add_edge("plan_to_json_translator", END)

        return builder.compile()

    @log_execution_time("bpmn_generation_task_flow.generate")
    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        model_type = context.override_model_type or ModelType.Anthropic
        planner_model_name = self._get_model_name(model_type)
        translator_model_name = self._get_light_model_name(LightModelType.GoogleFlash)

        current_bpmn = context.current_bpmn.strip() if context.current_bpmn is not None else ""
        current_bpmn = self.common_config["default_bpmn_xml"] if current_bpmn == "" else self._remove_bpmn_diagram(current_bpmn)

        graph = self._build_agent_graph()
        input_state = AgentState(
            user_request=context.user_request,
            current_bpmn=current_bpmn,
            planner_model_name=planner_model_name,
            translator_model_name=translator_model_name,
            plan="",
            translated_bpmn="",
            chat_history=context.chat_history,
            relevant_elements=None,
            image_data=context.image_data,
            document_data=context.document_data,
            usage_token=[],
            tool=Tool.UNSUPPORTED,
        )

        result_state = await graph.ainvoke(input_state)
        # print("Token usage in flow task {}".format(result_state["usage_token"]))
        translated_bpmn = result_state["translated_bpmn"].strip("'")
        return await self._validate_output(context.support_validation, current_bpmn, translated_bpmn, result_state["tool"], translator_model_name)

    @log_execution_time("bpmn_generation_task_flow.agent_run")
    async def run(self, state: BusinessProcessAgentState, config: RunnableConfig):
        message_emitter = config.get("configurable", {})["message_emitter"]
        user_request = await self.custom_stream(state, message_emitter)

        context = state["context"]

        model_type = context.override_model_type or ModelType.Anthropic
        planner_model_name = self._get_model_name(model_type)
        translator_model_name = self._get_light_model_name(LightModelType.GoogleFlash)

        current_bpmn = context.current_bpmn.strip() if context.current_bpmn is not None else ""
        current_bpmn = self.common_config["default_bpmn_xml"] if current_bpmn == "" else self._remove_bpmn_diagram(current_bpmn)

        graph = self._build_agent_graph()
        input_state = AgentState(
            user_request=user_request,
            current_bpmn=current_bpmn,
            planner_model_name=planner_model_name,
            translator_model_name=translator_model_name,
            plan="",
            translated_bpmn="",
            chat_history=context.chat_history,
            relevant_elements=None,
            image_data=context.image_data,
            document_data=context.document_data,
            usage_token=[],
            tool=Tool.UNSUPPORTED,
        )

        result_state = await graph.ainvoke(input_state)
        # print("Token usage in flow task {}".format(result_state["usage_token"]))
        translated_bpmn = result_state["translated_bpmn"].strip("'")
        result = await self._validate_output(context.support_validation, current_bpmn, translated_bpmn, result_state["tool"], translator_model_name)

        del result["title"]
        del result["explanation"]
        await message_emitter.emit_message(jsonable_encoder(result), "result")

        tool_results = state.get("tool_results") or []
        tool_results.append(result)

        return {"tool_results": tool_results, "current_tool_index": state["current_tool_index"] + 1}

    @log_execution_time("bpmn_generation_task_flow.validate_output")
    async def _validate_output(self, validate, current_bpmn: str, translated_bpmn: str, tool: Tool, translator_model_name: str) -> ToolResult:
        try:
            if validate:
                validation_result = VALIDATOR.validate_json(current_bpmn, translated_bpmn)
            else:
                validation_result = {"valid": True, "error_message": None}
            if validation_result["valid"]:
                result_obj = json.loads(translated_bpmn)
                return BpmnGenerationToolResult(
                    tool=tool,
                    title=result_obj.get("title"),
                    explanation=result_obj["explanation"],
                    add=result_obj["add"],
                    update=result_obj["update"],
                    delete=result_obj["delete"],
                )

            LOGGER.warning(f"JSON isn't valid: {validation_result['error_message']}. Attempting to fix the JSON.")
            error_message = validation_result["error_message"] or "JSON error"
            fixed_result = await self._fix_bpmn("json", translated_bpmn, error_message, translator_model_name, current_bpmn)
            fixed_result_json = fixed_result.strip("```json\n").strip("\n```")
            fix_validation_result = VALIDATOR.validate_json(current_bpmn, fixed_result_json)

            if fix_validation_result["valid"]:
                result_obj = json.loads(fixed_result_json)
                return BpmnGenerationToolResult(
                    tool=tool,
                    title=result_obj.get("title"),
                    explanation=result_obj["explanation"],
                    add=result_obj["add"],
                    update=result_obj["update"],
                    delete=result_obj["delete"],
                )

            LOGGER.error(
                f"Failed to generate valid BPMN JSON. An exception occurred after multiple creation attempts: {fix_validation_result['error_message']}"
            )
            return BpmnGenerationToolResult(
                tool=tool,
                title=None,
                explanation=CANNOT_PROCESS_REQUEST_EXPLANATION,
                add=[],
                update=[],
                delete=[],
            )
        except json.JSONDecodeError as e:
            LOGGER.error(f"Invalid JSON format: {str(e)}")
            return BpmnGenerationToolResult(
                tool=tool,
                title=None,
                explanation=CANNOT_PROCESS_REQUEST_EXPLANATION,
                add=[],
                update=[],
                delete=[],
            )

    def _get_file_mime_type(self, base64_data: str) -> tuple[str, str | None]:
        try:
            if not base64_data:
                return "", "No file data provided"

            # Match both image and document data URI patterns
            data_url_pattern = r"^data:([a-zA-Z0-9\-\/\.]+);base64,"
            match = re.match(data_url_pattern, base64_data)
            if match:
                mime_type = match.group(1).lower()
                if mime_type in (DIRECT_UPLOAD_MIME_TYPES | TEXT_EXTRACTION_MIME_TYPES):
                    return mime_type, None
                else:
                    error_msg = f"Unsupported file format: {mime_type}"
                    LOGGER.warning(error_msg)
                    return "", error_msg

            return "", "Invalid file format: missing MIME type prefix"

        except Exception as e:
            error_msg = f"Unexpected error in file type detection: {str(e)}"
            LOGGER.error(error_msg)
            return "", error_msg
