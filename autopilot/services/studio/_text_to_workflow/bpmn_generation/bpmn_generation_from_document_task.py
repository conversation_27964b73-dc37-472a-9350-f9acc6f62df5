import asyncio
import base64
import binascii
import io
import os
import pathlib
import re
import tempfile
import xml.etree.ElementTree as ET
import zipfile
from typing import List

import langchain.schema
import langchain.schema.messages
from markitdown import MarkItDown

from services.studio._text_to_workflow.bpmn_generation.bpmn_base_task import BpmnBaseTask
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_constants import (
    DIRECT_UPLOAD_MIME_TYPES,
    MAX_FILE_SIZE_MB,
    MIME_TO_EXTENSION,
    MIME_TO_READABLE_NAME,
    TEXT_EXTRACTION_MIME_TYPES,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_generation_schema import (
    BpmnGenerationToolResult,
    BpmnRequestContext,
    ModelType,
    Tool,
    ToolResult,
)
from services.studio._text_to_workflow.bpmn_generation.bpmn_validator import BpmnValidator
from services.studio._text_to_workflow.utils.inference.llm_schema import TokenUsage
from services.studio._text_to_workflow.utils.telemetry_utils import (
    AppInsightsLogger,
    log_execution_time,
)


class DocumentProcessingError(Exception):
    """Document processing error."""

    pass


class BpmnGenerationFromDocumentTask(BpmnBaseTask):
    """Generate BPMN from documents and images."""

    # Visio XML namespaces
    VISIO_NAMESPACES = {"v2003": "http://schemas.microsoft.com/visio/2003/core", "v2013": "http://schemas.microsoft.com/office/visio/2012/main"}

    def __init__(self) -> None:
        """Initialize task with config and validator."""
        super().__init__("generation_prompt.yaml")
        self._validator = BpmnValidator()
        self._logger = AppInsightsLogger()

        # Initialize config attributes
        self.bpmn_image_example: str = ""
        self.supported_element_examples: str = ""
        self.convert_document_bpmn_examples: str = ""

        self._load_configuration_files()
        self._logger.info("Initialized BpmnGenerationFromDocumentTask")

    def _load_configuration_files(self) -> None:
        """Load BPMN generation config files."""
        config_path = pathlib.Path(__file__).parent / "config"
        configs = {
            "bpmn_image_example": "bpmn_image_example.json",
            "supported_element_examples": "supported_element_examples.md",
            "convert_document_bpmn_examples": "convert_document_bpmn_examples.md",
        }

        for attr, filename in configs.items():
            setattr(self, attr, BpmnBaseTask.read_content((config_path / filename).absolute()))

    @log_execution_time("document_task.detect_and_validate_file")
    def _detect_and_validate_file(self, file_data: str) -> str:
        """Detect MIME type and validate file size."""
        if not file_data:
            raise DocumentProcessingError("No file data provided")

        try:
            base64_content = self._extract_base64_content(file_data)
            file_bytes = base64.b64decode(base64_content)
            file_size_mb = len(file_bytes) / (1024 * 1024)

            if file_size_mb > MAX_FILE_SIZE_MB:
                raise DocumentProcessingError(f"File size ({file_size_mb:.1f}MB) exceeds maximum {MAX_FILE_SIZE_MB}MB")

            # Detect file type
            if file_data.startswith("data:"):
                mime_type = self._extract_mime_from_data_uri(file_data)
            else:
                mime_type = self._detect_from_magic_bytes(file_bytes)

            if mime_type not in (DIRECT_UPLOAD_MIME_TYPES | TEXT_EXTRACTION_MIME_TYPES):
                raise DocumentProcessingError(f"Unsupported file type: {mime_type}")

            self._logger.info(f"File validated: {mime_type}, {file_size_mb:.2f}MB")
            return mime_type

        except binascii.Error:
            raise DocumentProcessingError("Invalid base64 file data")
        except Exception as e:
            if isinstance(e, DocumentProcessingError):
                raise
            raise DocumentProcessingError(f"File validation failed: {str(e)}")

    def _extract_mime_from_data_uri(self, data_uri: str) -> str:
        """Extract MIME type from data URI format."""
        match = re.match(r"^data:([^;]+);base64,", data_uri)
        if not match:
            raise DocumentProcessingError("Invalid data URI format")
        return match.group(1).lower()

    def _detect_from_magic_bytes(self, file_bytes: bytes) -> str:
        """Detect file type from magic byte signatures."""
        patterns = [
            # Documents
            (lambda b: b.startswith(b"%PDF"), "application/pdf"),
            (lambda b: b.startswith(b"PK\x03\x04") and b"word/" in b[:1024], "application/vnd.openxmlformats-officedocument.wordprocessingml.document"),
            (lambda b: b.startswith(b"PK\x03\x04") and b"visio/" in b[:1024], "application/vnd.ms-visio.drawing"),
            (lambda b: b.startswith(b"\xd0\xcf\x11\xe0"), "application/msword"),
            (lambda b: b.startswith(b"<?xml") and b"VisioDocument" in b[:1024], "application/vnd.visio"),
            # Images
            (lambda b: b.startswith(b"\x89PNG"), "image/png"),
            (lambda b: b.startswith(b"\xff\xd8\xff"), "image/jpeg"),
            (lambda b: b.startswith(b"RIFF") and b"WEBP" in b[:12], "image/webp"),
            (lambda b: b.startswith(b"GIF8"), "image/gif"),
        ]

        for pattern_func, mime_type in patterns:
            if pattern_func(file_bytes):
                return mime_type
        return "application/octet-stream"

    def _extract_base64_content(self, file_data: str) -> str:
        """Extract base64 content from file data."""
        return file_data.split(",", 1)[1] if "," in file_data and file_data.startswith("data:") else file_data

    @log_execution_time("document_task.process_office_document")
    async def _process_office_document(self, file_data: str, mime_type: str) -> str:
        """Extract text from office documents."""
        try:
            base64_content = self._extract_base64_content(file_data)
            document_bytes = base64.b64decode(base64_content)
            file_extension = MIME_TO_EXTENSION.get(mime_type)

            if not file_extension:
                raise DocumentProcessingError(f"No extension mapping for {mime_type}")

            # Choose extraction method based on file type
            if mime_type in ["application/vnd.ms-visio.drawing", "application/vnd.visio"]:
                text = self._extract_visio_text(document_bytes, file_extension)
            else:
                text = await self._extract_text_with_markitdown(document_bytes, file_extension)

            # Handle minimal content
            readable_type = MIME_TO_READABLE_NAME.get(mime_type, mime_type)
            if not text or len(text.strip()) < 10:
                self._logger.warning(f"Minimal content from {mime_type}")
                return f"# DOCUMENT CONTENT EXTRACTED FROM {readable_type}\n\n[Minimal text content extracted - document may contain primarily images, tables, or formatting elements. Please provide a document with clear business process text content for optimal BPMN generation.]"

            formatted_text = f"# DOCUMENT CONTENT EXTRACTED FROM {readable_type}\n\n{text}"
            self._logger.info(f"Extracted {len(text)} chars from {readable_type}")
            return formatted_text

        except Exception as e:
            if isinstance(e, DocumentProcessingError):
                raise
            raise DocumentProcessingError(f"Office document processing failed: {str(e)}")

    async def _extract_text_with_markitdown(self, document_bytes: bytes, file_extension: str) -> str:
        """Extract text using MarkItDown with temp file."""
        temp_file = None
        try:
            # Create temp file for MarkItDown
            temp_file = tempfile.NamedTemporaryFile(suffix=file_extension, delete=False)
            temp_file.write(document_bytes)
            temp_file.flush()
            temp_file.close()

            # Run MarkItDown in executor
            def run_markitdown(file_path: str) -> str:
                markitdown = MarkItDown()
                result = markitdown.convert(file_path)
                return result.text_content or ""

            loop = asyncio.get_event_loop()
            extracted_text = await loop.run_in_executor(None, run_markitdown, temp_file.name)

            if not extracted_text.strip():
                self._logger.warning(f"No text content extracted from {file_extension} using MarkItDown")
                return f"[No readable text content found in {file_extension} document]"

            # Clean up the extracted text
            extracted_text = extracted_text.strip()

            self._logger.info(f"Successfully extracted {len(extracted_text)} characters from {file_extension} using MarkItDown")
            return extracted_text

        except Exception as e:
            self._logger.error(f"MarkItDown temp file extraction failed: {e}")
            raise DocumentProcessingError(f"MarkItDown temp file extraction failed: {str(e)}")
        finally:
            if temp_file and os.path.exists(temp_file.name):
                try:
                    os.unlink(temp_file.name)
                except OSError as cleanup_error:
                    self._logger.warning(f"Failed to clean up temporary file: {cleanup_error}")

    def _extract_visio_text(self, document_bytes: bytes, file_extension: str) -> str:
        """Extract text from Visio files."""
        try:
            return self._extract_visio_content(document_bytes, file_extension)
        except Exception as e:
            raise DocumentProcessingError(f"Visio text extraction failed: {str(e)}")

    @log_execution_time("bpmn_generation_from_document_task.extract_visio_content")
    def _extract_visio_content(self, data: bytes, ext: str) -> str:
        """Extract content from VSDX or VDX files."""
        try:
            if ext == ".vsdx":
                return self._extract_vsdx_content(data)
            elif ext == ".vdx":
                return self._extract_vdx_content(data)
            return f"[Unsupported Visio format: {ext}]"
        except Exception as e:
            self._logger.warning(f"Visio extraction error: {e}")
            return f"[Visio extraction error: {str(e)}]"

    @log_execution_time("bpmn_generation_from_document_task.extract_vsdx_content")
    def _extract_vsdx_content(self, data: bytes) -> str:
        """Extract content from VSDX files using improved XML parsing."""
        items = []
        try:
            with zipfile.ZipFile(io.BytesIO(data)) as z:
                names = sorted(z.namelist())
                for name in names:
                    if not name.startswith("visio/"):
                        continue
                    if name.endswith(".xml"):
                        try:
                            content = z.read(name).decode("utf-8", errors="ignore")
                            if "pages/" in name:
                                items.append(f"=== PAGE: {name} ===")
                                items.extend(self._parse_visio_page_xml(content))
                            elif "masters/" in name:
                                items.append(f"=== MASTER: {name} ===")
                                items.extend(self._parse_visio_master_xml(content))
                        except Exception as e:
                            self._logger.warning(f"Could not parse {name}: {e}")
        except zipfile.BadZipFile as e:
            raise DocumentProcessingError(f"Invalid VSDX archive: {e}")

        if items:
            self._logger.info(f"Successfully extracted {len(items)} content items from VSDX")
            return "\n".join(items)
        else:
            self._logger.warning("No content extracted from VSDX file structure")
            return "[No content extracted from VSDX]"

    @log_execution_time("bpmn_generation_from_document_task.extract_vdx_content")
    def _extract_vdx_content(self, data: bytes) -> str:
        """Extract content from VDX XML."""
        items = []
        try:
            xml = data.decode("utf-8", errors="ignore")
            root = ET.fromstring(xml)
            ns = {"visio": self.VISIO_NAMESPACES["v2003"]}

            # Extract pages
            for page in root.findall(".//visio:Page", ns):
                page_name = page.get("Name", "Unknown")
                items.append(f"=== PAGE: {page_name} ===")
                for shape in page.findall(".//visio:Shape", ns):
                    shape_name, texts, cells = self._parse_shape(shape, ns)
                    items.extend(f"Shape '{shape_name}': {t}" for t in texts)
                    items.extend(f"Shape '{shape_name}' {c}" for c in cells)

            # Extract masters
            for master in root.findall(".//visio:Master", ns):
                master_name = master.get("Name", "Master")
                items.append(f"=== MASTER: {master_name} ===")
                for shape in master.findall(".//visio:Shape", ns):
                    _, texts, _ = self._parse_shape(shape, ns)
                    items.extend(f"Master: {t}" for t in texts)

        except Exception as e:
            raise DocumentProcessingError(f"VDX parsing error: {e}")

        return "\n".join(items) or "[No content extracted from VDX]"

    def _parse_visio_page_xml(self, xml_content: str) -> List[str]:
        """Parse Visio page XML for shape content."""
        content = []
        try:
            root = ET.fromstring(xml_content)

            # Try with namespace first
            for ns_url in self.VISIO_NAMESPACES.values():
                ns = {"visio": ns_url}
                shapes = root.findall(".//visio:Shape", ns)
                if shapes:
                    for shape in shapes:
                        shape_name, texts, cells = self._parse_shape(shape, ns)
                        content.extend(f"Shape '{shape_name}': {t}" for t in texts)
                        content.extend(f"Shape '{shape_name}' {c}" for c in cells)
                    break

            # Fallback to no namespace
            if not content:
                for shape in root.findall(".//Shape"):
                    shape_id = shape.get("ID", "Unknown")
                    shape_name = shape.get("Name", f"Shape_{shape_id}")

                    for text_elem in shape.findall(".//Text"):
                        text = self._extract_all_text(text_elem)
                        if text:
                            content.append(f"Shape '{shape_name}': {text}")

                    for cell in shape.findall(".//Cell"):
                        cell_name, cell_value = cell.get("N", ""), cell.get("V", "")
                        if cell_name and cell_value and any(k in cell_name.lower() for k in ["process", "description", "notes", "name", "text"]):
                            content.append(f"Shape '{shape_name}' {cell_name}={cell_value}")

        except ET.ParseError as e:
            self._logger.warning(f"XML parsing error in Visio page: {e}")

        return content

    def _extract_all_text(self, elem) -> str:
        """Extract all text content from an XML element, including nested elements."""
        parts = []
        if elem.text:
            parts.append(elem.text.strip())
        for child in elem:
            child_text = self._extract_all_text(child)
            if child_text:
                parts.append(child_text)
            if child.tail:
                parts.append(child.tail.strip())
        return " ".join(parts)

    def _parse_visio_master_xml(self, xml_content: str) -> List[str]:
        """Parse Visio master XML for templates."""
        content = []
        try:
            root = ET.fromstring(xml_content)
            master_name = root.get("Name", "Unknown Master")
            content.append(f"Master Template: {master_name}")

            for ns_url in self.VISIO_NAMESPACES.values():
                ns = {"visio": ns_url}
                text_elements = root.findall(".//visio:Text", ns)
                if text_elements:
                    for text_elem in text_elements:
                        text = self._extract_all_text(text_elem)
                        if text:
                            content.append(f"Template: {text}")
                    break

            # Fallback to no namespace
            if len(content) <= 1:
                for text_elem in root.findall(".//Text"):
                    text = self._extract_all_text(text_elem)
                    if text:
                        content.append(f"Template: {text}")

        except ET.ParseError as e:
            self._logger.warning(f"XML parsing error in Visio master: {e}")

        return content

    def _parse_shape(self, shape, ns):
        """Parse Visio shape for name, text, and properties."""
        shape_id = shape.get("ID", "Unknown")
        shape_name = shape.get("Name", f"Shape_{shape_id}")
        texts, cells = [], []

        for text_elem in shape.findall(".//visio:Text", ns):
            text = self._extract_all_text(text_elem)
            if text:
                texts.append(text)

        for cell in shape.findall(".//visio:Cell", ns):
            cell_name, cell_value = cell.get("N", ""), cell.get("V", "")
            if cell_name and cell_value and any(k in cell_name.lower() for k in ["process", "description", "notes", "name", "text"]):
                cells.append(f"{cell_name}={cell_value}")

        return shape_name, texts, cells

    def _prepare_direct_upload_content(self, file_data: str, mime_type: str) -> dict:
        """Prepare content for direct LLM upload."""
        encoded_str = self._extract_base64_content(file_data)
        if mime_type.startswith("application/"):
            return {"type": mime_type, "data": encoded_str}
        return {"type": "image_url", "image_url": {"url": f"data:{mime_type};base64,{encoded_str}"}}

    @log_execution_time("document_task.generate")
    async def generate(self, context: BpmnRequestContext) -> ToolResult:
        """Generate BPMN diagram from documents and images."""
        try:
            self._logger.info("Starting document BPMN generation")

            if not context.document_data:
                return self._create_error_result("No Document Provided", "Please provide a document or image file.")

            file_data = context.document_data.decode("utf-8") if isinstance(context.document_data, bytes) else context.document_data
            mime_type = self._detect_and_validate_file(file_data)

            if mime_type in DIRECT_UPLOAD_MIME_TYPES:
                model_name = self._get_model_name(ModelType.Google)
                return await self._process_direct_upload(context, file_data, mime_type, model_name)
            elif mime_type in TEXT_EXTRACTION_MIME_TYPES:
                model_type = context.override_model_type or ModelType.Anthropic
                model_name = self._get_model_name(model_type)
                return await self._process_with_extraction(context, file_data, mime_type, model_name)
            else:
                return self._create_error_result("Unsupported File Type", f"File type {mime_type} is not supported.")

        except DocumentProcessingError as e:
            self._logger.error(f"Document processing error: {e}")
            return self._create_error_result("Processing Error", str(e))
        except Exception as e:
            self._logger.error(f"Unexpected error in document generation: {e}")
            return self._create_error_result("Generation Failed", f"Failed to generate BPMN: {str(e)}")

    async def _process_direct_upload(self, context: BpmnRequestContext, file_data: str, mime_type: str, model_name: str) -> BpmnGenerationToolResult:
        """Process files for direct LLM upload (PDFs, images)."""
        readable_type = MIME_TO_READABLE_NAME.get(mime_type, mime_type)
        try:
            self._logger.info(f"Processing direct upload: {mime_type}")

            system_message = self._system_template(self.config["prompt"]["system_template"]["convert_document"]).format(
                bpmn_patch_schema=self.bpmn_patch_schema,
                supported_element_examples=self.supported_element_examples,
                convert_document_bpmn_examples=self.convert_document_bpmn_examples,
            )

            # Format user text
            user_text = self.config["prompt"]["user_template"]["document_direct"].format(user_request=context.user_request)

            content_item = self._prepare_direct_upload_content(file_data, mime_type)
            user_message = langchain.schema.HumanMessage(content=[{"type": "text", "text": user_text}, content_item])

            result, usage = await self._call_llm(system_message, user_message, model_name)
            result_json = self._extract_json_from_response(result)

            if not result_json.strip():
                self._logger.error(f"Empty JSON response from LLM for {readable_type}")
                return self._create_error_result("Empty Response", f"LLM returned empty response for {readable_type}")

            self._logger.info(f"Generated BPMN from {readable_type}")
            return await self._create_bpmn_result(context, result_json, usage, model_name)

        except Exception as e:
            self._logger.error(f"Direct upload failed: {e}")
            return self._create_error_result("Direct Upload Failed", f"Failed to process {mime_type}: {str(e)}")

    async def _process_with_extraction(self, context: BpmnRequestContext, file_data: str, mime_type: str, model_name: str) -> BpmnGenerationToolResult:
        """Process files requiring text extraction (Office, Visio)."""
        readable_type = MIME_TO_READABLE_NAME.get(mime_type, mime_type)
        try:
            self._logger.info(f"Text extraction: {mime_type}")

            extracted_text = await self._process_office_document(file_data, mime_type)

            system_message = self._system_template(self.config["prompt"]["system_template"]["convert_document"]).format(
                bpmn_patch_schema=self.bpmn_patch_schema,
                supported_element_examples=self.supported_element_examples,
                convert_document_bpmn_examples=self.convert_document_bpmn_examples,
            )

            user_message = self._human_template(self.config["prompt"]["user_template"]["document_extracted"]).format(
                user_request=context.user_request, extracted_text=extracted_text
            )

            result, usage = await self._call_llm(system_message, user_message, model_name)
            result_json = self._extract_json_from_response(result)

            if not result_json.strip():
                self._logger.error(f"Empty JSON response from LLM for {readable_type}")
                return self._create_error_result("Empty Response", f"LLM returned empty response for {readable_type}")

            self._logger.info(f"Generated BPMN from {readable_type} document")
            return await self._create_bpmn_result(context, result_json, usage, model_name)

        except Exception as e:
            self._logger.error(f"Direct upload processing failed: {e}")
            return self._create_error_result("Direct Upload Failed", f"Failed to process {mime_type}: {str(e)}")

    def _extract_json_from_response(self, result: str) -> str:
        """Extract JSON from LLM response with robustness for wrapped responses."""
        # First, try to extract from code blocks
        json_match = re.search(r"```json\s*\n(.*?)\n```", result, re.DOTALL)
        if json_match:
            result_json = json_match.group(1).strip()
        else:
            result_json = result.strip("```json\n").strip("\n```").strip()

        return result_json

    async def _create_bpmn_result(self, context: BpmnRequestContext, result_json: str, usage: TokenUsage, model_name: str) -> BpmnGenerationToolResult:
        """Create BPMN result from validated output."""
        try:
            current_bpmn = self.common_config["default_bpmn_xml"] if context.current_bpmn == "" else context.current_bpmn
            output = await self._validate_output(context.support_validation, current_bpmn, result_json, usage, model_name)

            deleted_elements = output.get("delete") or []
            process_id_to_delete = self.extract_process_id(current_bpmn)
            if process_id_to_delete:
                deleted_elements.append({"id": process_id_to_delete, "type": "bpmn:process"})

            return BpmnGenerationToolResult(
                tool=Tool.CONVERT_DOCUMENT,
                title=output.get("title"),
                explanation=output["explanation"],
                add=output.get("add", []),
                update=output.get("update", []),
                delete=deleted_elements,
            )
        except Exception as e:
            self._logger.error(f"Failed to create BPMN result: {e}")
            return self._create_error_result("Invalid Response", f"Failed to parse LLM response: {str(e)}")

    def _create_error_result(self, title: str, explanation: str) -> BpmnGenerationToolResult:
        """Create standardized error result."""
        return BpmnGenerationToolResult(
            tool=Tool.CONVERT_DOCUMENT,
            title=title,
            explanation=explanation,
            add=[],
            update=[],
            delete=[],
        )

    @log_execution_time("document_task.process_multiple_files")
    async def _process_multiple_files(self, files: List[str]) -> List[str]:
        """Process multiple files in parallel."""
        import time

        start = time.time()

        async def process_single_file(file_data: str) -> str:
            try:
                mime_type = self._detect_and_validate_file(file_data)
                if mime_type in TEXT_EXTRACTION_MIME_TYPES:
                    return await self._process_office_document(file_data, mime_type)
                return ""
            except Exception as e:
                self._logger.warning(f"File processing failed: {e}")
                return ""

        processed_texts = await asyncio.gather(*[process_single_file(file) for file in files])

        end = time.time()
        self._logger.info(f"Time to process {len(files)} documents: {end - start} seconds")

        return [text for text in processed_texts if text]
