import argparse
import json
from pathlib import Path

import typing_extensions as t
from lxml import etree

from services.studio._text_to_workflow.common.schema import TargetFramework
from services.studio._text_to_workflow.common.walkers import ActivityIdCollectorIncludingSyntheticSteps
from services.studio._text_to_workflow.common.workflow import Workflow
from services.studio._text_to_workflow.utils import formatting_utils, paths
from services.studio._text_to_workflow.utils.workflow_utils import get_display_names
from services.studio._text_to_workflow.utils.yaml_utils import yaml_load

prefix_map = {
    "": "http://schemas.microsoft.com/netfx/2009/xaml/activities",
    "isactr": "http://schemas.uipath.com/workflow/integration-service-activities/isactr",
    "mc": "http://schemas.openxmlformats.org/markup-compatibility/2006",
    "sap": "http://schemas.microsoft.com/netfx/2009/xaml/activities/presentation",
    "sap2010": "http://schemas.microsoft.com/netfx/2010/xaml/activities/presentation",
    "scg": "clr-namespace:System.Collections.Generic;assembly=System.Private.CoreLib",
    "uasf": "clr-namespace:UiPath.Activities.System.FileOperations;assembly=UiPath.System.Activities",
    "ui": "http://schemas.uipath.com/workflow/activities",
    "uisape": "clr-namespace:UiPath.IntelligentOCR.StudioWeb.Activities.PDF.ExtractPDFText;assembly=UiPath.IntelligentOCR.StudioWeb.Activities",
    "upr": "clr-namespace:UiPath.Platform.ResourceHandling;assembly=UiPath.Platform",
    "x": "http://schemas.microsoft.com/winfx/2006/xaml",
    "uiascb": "clr-namespace:UiPath.IntegrationService.Activities.SWEntities.C3A01EAE649_generateChatCompletion_Create.Bundle;"
    "assembly=C3A01EAE649_generate.XEw3O3vyE8K1tvYuZ2hXvFi",
}

downloads_root = paths.get_autopilot_samples_feature_dataset_path("Downloads", None, None)


def wfgen_path_to_converted_path(wfgen_path: Path) -> Path:
    wf_framework: TargetFramework = t.cast(TargetFramework, wfgen_path.parts[-3])
    fname_stem = wfgen_path.stem
    converted_path = paths.get_converted_dataset_path(wf_framework) / fname_stem / "Main.yaml"
    if converted_path.exists():
        return converted_path
    raise ValueError("Converted path does not exist.")


def wfgen_path_to_xaml_path(wfgen_path: Path) -> Path:  # TODO: move this to paths
    fname_stem = wfgen_path.stem
    fname_parts = fname_stem.split("_")
    xaml_path = downloads_root
    remainder = ""
    for part in fname_parts:
        if remainder == "":
            remainder = part
        else:
            remainder = f"{remainder}_{part}"

        if (xaml_path / remainder).exists():
            xaml_path = xaml_path / remainder
            remainder = ""
            continue
        if (xaml_path / f"{remainder}.xaml").exists():
            xaml_path = xaml_path / f"{remainder}.xaml"
            remainder = ""
            break
    if remainder == "":
        if xaml_path.as_posix().endswith(".xaml"):
            return xaml_path
        if (xaml_path / "Main.xaml").exists():
            return xaml_path / "Main.xaml"
    raise ValueError("Cannot find xaml path.")


def wfgen_path_to_project_json_path(wfgen_path: Path) -> Path:  # TODO: move these to paths once matured and tested
    xaml_path = wfgen_path_to_xaml_path(wfgen_path)
    if xaml_path is None:
        raise ValueError("Could not find xaml path.")
    folder_path = xaml_path.parent
    while len(folder_path.as_posix()) > len(downloads_root.as_posix()):
        if (folder_path / "project.json").exists():
            return folder_path / "project.json"
        folder_path = folder_path.parent
    raise ValueError("Cannot find project.json.")


def commit_to_xaml(
    wfgen_path: Path,
    query: str | None = None,
    plan: str | None = None,
    dry_run: bool = False,
    verbose: bool = False,
):
    if query is not None:
        project_json_path = wfgen_path_to_project_json_path(wfgen_path)
        with project_json_path.open("r", encoding="utf-8-sig") as fin:
            project_json = json.load(fin)
        project_json["description"] = query
        if not dry_run:
            with project_json_path.open("w") as fp:
                json.dump(project_json, fp, indent=2)
    if plan is not None:
        converted_path = wfgen_path_to_converted_path(wfgen_path)
        xaml_path = wfgen_path_to_xaml_path(wfgen_path)

        # this is such that we revert the doubling of the quotes
        steps = get_display_names(formatting_utils.unescape_quotes(plan))
        converted = yaml_load(converted_path)
        activity_ids = list(ActivityIdCollectorIncludingSyntheticSteps().get_ids(Workflow("", "", converted)))
        if len(steps) != len(activity_ids):
            print(f"{len(steps)=} != {len(activity_ids)=}")
            raise ValueError("Mismatching number of steps with number of activity ids.")

        root = etree.parse(xaml_path)  # type: ignore
        for step, activity_id in zip(steps, activity_ids, strict=False):
            if activity_id.startswith(ActivityIdCollectorIncludingSyntheticSteps.SYNTHETIC_STEP_ACTIVITY_ID.rsplit(".", 1)[-1] + "_"):
                continue  # skip synthetic steps
            if activity_id.startswith("ManualTrigger_"):
                continue
            # e.g. activity_id = "ManualTrigger_1"
            elem = root.find(f'.//*[@sap2010:WorkflowViewState.IdRef="{activity_id}"]', prefix_map)
            if elem is None:
                print(f"Cannot find activity_id: {activity_id}")
                continue
            # print(elem)
            elem.set("DisplayName", step)
        if not dry_run:
            content_before = xaml_path.read_text()
            # tried with .tostring() but there were some problems with the encoding
            root.write(xaml_path, encoding="utf-8")
            content_after = xaml_path.read_text(encoding="utf-8")
            content_after = content_after.replace("<x:String/>", "<x:String></x:String>")
            xaml_path.write_text(content_after, encoding="utf-8")

            content_before = content_before.replace(" />", "/>")
            content_before = content_before.split("\n", 1)[-1]
            content_after = content_after.split("\n", 1)[-1]

            if verbose and content_before != content_after:
                from services.studio._text_to_workflow.utils.workflow_utils import get_diff

                print(xaml_path)
                # print(get_diff(content_before, content_after))
                print(get_diff(content_before, content_after, span=5))
                print("\n" * 5)


def yaml_to_xaml(
    wfgen_path: Path,
    dry_run: bool = False,
    verbose: bool = False,
):
    wfgen = yaml_load(wfgen_path)
    commit_to_xaml(wfgen_path, wfgen["query"], wfgen["plan"], dry_run=dry_run, verbose=verbose)


def yaml_to_xaml_entire_dataset(
    target_framework: TargetFramework,
    subset: str | None = None,
    dry_run: bool = False,
    verbose: bool = False,
):
    if subset is None:
        yaml_to_xaml_entire_dataset(target_framework, "train", dry_run=dry_run)
        yaml_to_xaml_entire_dataset(target_framework, "test", dry_run=dry_run)
        return
    dataset_path = paths.get_workflow_generation_dataset_path(target_framework, subset)
    listing = sorted(dataset_path.glob("*.yaml"))
    for yaml_path in listing:
        try:
            yaml_to_xaml(yaml_path, dry_run=dry_run, verbose=verbose)
        except Exception as e:
            print(f"Failed to convert {yaml_path}: {e}")


if __name__ == "__main__":
    ap = argparse.ArgumentParser()
    ap.add_argument("job", choices=["file", "dataset"], help="file: convert a single file, dataset: convert a dataset")
    ap.add_argument("--dry-run", action="store_true", help="Do not write to xaml files.")
    ap.add_argument("--verbose", action="store_true", help="Print verbose differences.")
    args, unknown = ap.parse_known_args()

    if args.job == "dataset":
        ap.add_argument(
            "--target-framework",
            "--target_framework",
            "--framework",
            type=str,
            default="Portable",
            choices=["Portable", "Windows"],
            help="Target framework to generate for.",
        )
        ap.add_argument(
            "--subset",
            type=str,
            default=None,
            choices=["train", "test"],
            help="Subset of the dataset to process. If not provided, both train and test subsets will be processed.",
        )
        args = ap.parse_args()
        yaml_to_xaml_entire_dataset(args.target_framework, args.subset, args.dry_run, args.verbose)
    elif args.job == "file":
        ap.add_argument("wfgen_path", type=Path)
        yaml_to_xaml(args.wfgen_path, args.dry_run, args.verbose)
