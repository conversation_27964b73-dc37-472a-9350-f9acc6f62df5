# Process Design Document (PDD)

**Process Name:** Loan Processing Workflow
**Version:** 1.0
**Author:** <PERSON>
**Date:** 25 Jun 2025

## Table of Contents

1. Objective
2. Scope
3. Stakeholders & Actors
4. Definitions & Acronyms
5. High‑Level Process Overview
6. Detailed Process Description
   * 6.1 Application Intake & Completeness Validation
   * 6.2 Data Entry & Validation Checks
   * 6.3 Loan Eligibility Analysis & Determination
   * 6.4 System Updates & Communication
7. Exception Handling & Escalations
8. Business Rules & Validation Matrix
9. Service‑Level Agreements (SLAs) & Timers
10. Assumptions & Constraints
11. Non‑Functional Requirements
12. Compliance & Audit Considerations
13. References
14. Appendix (BPMN Diagram, Field Mappings, State‑Specific Requirements)

## 1. Objective

To define the end‑to‑end automated workflow that processes a new loan application from intake to applicant notification, ensuring compliance with state regulations, data accuracy across integrated systems, and timely communication.

## 2. Scope

* **In‑Scope:** Residential loan applications submitted via Salesforce, data extraction from supporting documents, validations, LOS updates, eligibility analysis, contract generation, CRM updates, and applicant notifications.
* **Out‑of‑Scope:** Post‑closing activities, loan servicing, secondary market sale processes, manual back‑office audits beyond defined human review steps.

## 3. Stakeholders & Actors

| Role | Responsibility |
| --- | --- |
| **Applicant** | Submits required documents, responds to information requests. |
| **Loan Officer (Human Review)** | Reviews escalated validation issues & borderline eligibility cases. |
| **Compliance Analyst (Human Review)** | Confirms adherence to state regulations. |
| **RPA Bot – Intake** | Monitors Salesforce for new applications, launches extraction & validation sub‑flows. |
| **RPA Bot – LOS Updater** | Writes validated data to the Loan Origination System (LOS). |
| **RPA Bot – Analytics** | Performs risk calculations & retrieves transaction history concurrently. |
| **CRM Service** | Registers loan opportunity & personalizes applicant responses. |
| **Notification Service** | Sends email/SMS notifications. |

## 4. Definitions & Acronyms

| Term | Definition |
| --- | --- |
| **LOS** | Loan Origination System |
| **KYC** | Know Your Customer |
| **BPMN** | Business Process Model and Notation |
| **RPA** | Robotic Process Automation |

## 5. High‑Level Process Overview

1. **Trigger:** New loan application created in Salesforce.
2. **Stage 1:** Application intake, data extraction & completeness validation.
3. **Stage 2:** Data entry into LOS & concurrent validation checks.
4. **Stage 3:** Eligibility analysis & determination.
5. **Stage 4:** System updates, applicant communication, and process termination.

*A BPMN 2.0 diagram is provided in* ***Appendix A****.*

## 6. Detailed Process Description

### 6.1 Application Intake & Completeness Validation

| Item | Description |
| --- | --- |
| **Start Event** | **Message Start Event** – Salesforce “LoanApplicationCreated” record. |
| **Activities** | 1. **Data Extraction (Service Task):** Use AI Document Understanding to extract key data from complex documents (e.g., bank statements). |

1. **Create/Link Customer Record (Service Task):** Ensure applicant exists as a reference object in Salesforce; create if absent.
2. **Validate Documents (Sub‑Process):** Compare extracted data & uploaded documents against state‑specific completeness & validity rules. | | **Gateways** | **Exclusive Gateway – Validation Result**:
   • *Valid & Complete* → Proceed to Stage 2.
   • *Invalid/Missing* → **Escalate for Human Review** (User Task)
   – If reviewer confirms issue, send “Additional Info Required” request.
   – **Event‑Based Gateway:** Wait for **Message Intermediate Catch Event** (Updated Docs Received) **OR** **Timer Intermediate Catch Event** (5 days). | | **Escalations** | If timer elapses without response, process **ends** with status *Terminated – No Response*. |

### 6.2 Data Entry & Validation Checks

| Item | Description |
| --- | --- |
| **Pre‑condition** | Application marked *Complete & Valid*. |
| **Parallel Gateway** | Split into three concurrent paths: |

1. **Update LOS (Service Task)** – Write applicant & loan data.
2. **Risk Calculations (Service Task)** – Calculate DTI, credit score banding, collateral LTV, etc.
3. **Retrieve Transaction History (Service Task)** – Pull prior dealings & payment history. | | **Join Gateway** | **Parallel Join** waits for all paths to finish. |

### 6.3 Loan Eligibility Analysis & Determination

| Item | Description |
| --- | --- |
| **Eligibility Engine (Service Task)** | Execute underwriting rules & scoring model. |
| **Human Review (User Task – Optional)** | Triggered when score within ±5% of approval threshold or rules flagged. |
| **Exclusive Gateway – Decision** |  |

• **Approved** → Update LOS with approval, generate contract (Service Task).
• **Declined** → Mark application *Declined*. |

### 6.4 System Updates & Communication

| Item | Description |
| --- | --- |
| **CRM Update (Service Task)** | Register/Update loan opportunity record. |
| **Tailor Response (Service Task)** | Compose personalized approval/decline message. |
| **Notify Applicant (Service Task)** | Send email & SMS via Notification Service. |
| **End Event** | **Message End Event** – Status sent to applicant & systems. |

## 7. Exception Handling & Escalations

* **Document Extraction Failures:** Route to Loan Officer for manual keying.
* **System Outages (Salesforce/LOS):** Retry up to 3 times with 10‑minute intervals, else create incident ticket & notify support.
* **High Fraud Risk Scores:** Auto‑escalate to Compliance Analyst.

## 8. Business Rules & Validation Matrix (Excerpt)

| Rule ID | Description | Passed When |
| --- | --- | --- |
| VR‑001 | Proof of income provided | Bank statements contain net salary entries for last 3 months |
| VR‑028 | State‑specific ID validity | ID expiration date ≥ current date |
| EL‑101 | Min Credit Score | ≥ 640 |

*(Full matrix in* ***Appendix B****)*

## 9. SLAs & Timers

| Metric | Target |
| --- | --- |
| Intake validation turnaround | ≤ 15 minutes |
| Applicant response wait | **Timer:** 5 days (automatic termination) |
| Eligibility decision after complete app | ≤ 2 hours |

## 10. Assumptions & Constraints

* Salesforce & LOS APIs are available and authenticated.
* Document Understanding ML models achieve ≥ 90% extraction accuracy.
* State regulatory requirements provided are current and accurate.

## 11. Non‑Functional Requirements

* **Auditability:** Full trace logs of data changes & decision outcomes.
* **Security:** PII encrypted in transit & at rest (TLS 1.3, AES‑256).
* **Performance:** Workflow handles 500 concurrent applications.

## 12. Compliance & Audit Considerations

* Retain documents & decision logs for 7 years.
* Align with Fair Lending (Equal Credit Opportunity Act) guidelines.

## 13. References

* State Regulatory Requirements Handbook v2025.1
* Salesforce Object Schema – LoanApplication v3.2
* LOS Data Dictionary v2025‑Q2

## 14. Appendix

**Appendix A:** BPMN 2.0 diagram (placeholder – to be created in BPMN modeller).
**Appendix B:** Detailed Validation & Eligibility Rule Matrix.
**Appendix C:** Field Mapping Tables (Salesforce ↔ LOS ↔ CRM).

*End of Document*