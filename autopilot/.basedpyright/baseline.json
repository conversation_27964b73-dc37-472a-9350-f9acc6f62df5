{"files": {"./services/studio/_text_to_workflow/activity_config/activity_config_evaluation.py": [{"code": "reportReturnType", "range": {"startColumn": 32, "endColumn": 44, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 38, "endColumn": 66, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 23, "endColumn": 33, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 32, "endColumn": 51, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 121, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 34, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 34, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 38, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 11, "endColumn": 58, "lineCount": 1}}], "./services/studio/_text_to_workflow/activity_summary/activity_summary_dataset.py": [{"code": "reportArgumentType", "range": {"startColumn": 59, "endColumn": 71, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 56, "endColumn": 73, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 34, "endColumn": 39, "lineCount": 1}}], "./services/studio/_text_to_workflow/activity_summary/activity_summary_evaluation.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 25, "lineCount": 1}}], "./services/studio/_text_to_workflow/activity_summary/activity_summary_task.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 29, "endColumn": 45, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 62, "endColumn": 78, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v1/endpoints/activity_summary.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 29, "endColumn": 41, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 27, "endColumn": 37, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 80, "endColumn": 97, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 76, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v1/endpoints/expression_generation.py": [{"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 8, "endColumn": 25, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 80, "endColumn": 97, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 8, "endColumn": 25, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 80, "endColumn": 97, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v1/endpoints/testdata_generation.py": [{"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 8, "endColumn": 25, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 80, "endColumn": 97, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v1/endpoints/workflow_generation.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 51, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 51, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v2/endpoints/activity_summary.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 87, "endColumn": 99, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v2/endpoints/expression_generation.py": [{"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}], "./services/studio/_text_to_workflow/api/v2/endpoints/testdata_generation.py": [{"code": "reportGeneralTypeIssues", "range": {"startColumn": 12, "endColumn": 29, "lineCount": 1}}], "./services/studio/_text_to_workflow/bpmn_generation/bpmn_base_task.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 22, "lineCount": 1}}], "./services/studio/_text_to_workflow/bpmn_generation/bpmn_generation_endpoint.py": [{"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 62, "endColumn": 84, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 62, "endColumn": 84, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 67, "endColumn": 89, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 67, "endColumn": 89, "lineCount": 1}}], "./services/studio/_text_to_workflow/bpmn_generation/bpmn_telemetry_task.py": [{"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 104, "endColumn": 122, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 136, "endColumn": 155, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 54, "endColumn": 59, "lineCount": 1}}], "./services/studio/_text_to_workflow/bpmn_generation/bpmn_validator.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 22, "lineCount": 1}}], "./services/studio/_text_to_workflow/code_generation/code_generation_demo_retriever.py": [{"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 45, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 45, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 16, "endColumn": 9, "lineCount": 8}}, {"code": "reportArgumentType", "range": {"startColumn": 75, "endColumn": 90, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 129, "lineCount": 1}}], "./services/studio/_text_to_workflow/code_generation/code_generation_retriever.py": [{"code": "reportReturnType", "range": {"startColumn": 19, "endColumn": 23, "lineCount": 1}}], "./services/studio/_text_to_workflow/code_generation/code_generation_task.py": [{"code": "reportCallIssue", "range": {"startColumn": 30, "endColumn": 54, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 30, "endColumn": 54, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 33, "endColumn": 55, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 29, "endColumn": 55, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 30, "endColumn": 54, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 30, "endColumn": 54, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 25, "endColumn": 49, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 25, "endColumn": 49, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 24, "endColumn": 45, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 77, "endColumn": 84, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 28, "endColumn": 38, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 28, "endColumn": 38, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 16, "endColumn": 93, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 60, "endColumn": 66, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 41, "endColumn": 46, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 19, "endColumn": 73, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 34, "endColumn": 51, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 15, "endColumn": 32, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 67, "endColumn": 88, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 67, "endColumn": 88, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 17, "endColumn": 34, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 68, "endColumn": 89, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 68, "endColumn": 89, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 155, "endColumn": 159, "lineCount": 1}}], "./services/studio/_text_to_workflow/code_generation/evaluation/eval.py": [{"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 53, "endColumn": 58, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 16, "endColumn": 20, "lineCount": 1}}], "./services/studio/_text_to_workflow/code_generation/evaluation/eval_cases.py": [{"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 75, "endColumn": 77, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 99, "endColumn": 101, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 147, "endColumn": 149, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 123, "endColumn": 125, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 72, "endColumn": 74, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 93, "endColumn": 95, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 52, "endColumn": 54, "lineCount": 1}}, {"code": "reportInvalidStringEscapeSequence", "range": {"startColumn": 91, "endColumn": 93, "lineCount": 1}}], "./services/studio/_text_to_workflow/common/embeddingsdb.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 19, "lineCount": 1}}], "./services/studio/_text_to_workflow/common/schema.py": [{"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 23, "endColumn": 28, "lineCount": 1}}], "./services/studio/_text_to_workflow/common/typedefs.py": [{"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 36, "endColumn": 85, "lineCount": 1}}], "./services/studio/_text_to_workflow/common/typedefs_parser.py": [{"code": "reportReturnType", "range": {"startColumn": 16, "endColumn": 26, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 39, "endColumn": 45, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 44, "endColumn": 55, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 17, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 15, "endColumn": 50, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 60, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 29, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 40, "endColumn": 47, "lineCount": 1}}], "./services/studio/_text_to_workflow/common/walkers.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 13, "endColumn": 31, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 23, "endColumn": 55, "lineCount": 1}}], "./services/studio/_text_to_workflow/expression_generation/expression_generation_endpoint.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 30, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 23, "lineCount": 1}}], "./services/studio/_text_to_workflow/expression_generation/expression_generation_task.py": [{"code": "reportCallIssue", "range": {"startColumn": 27, "endColumn": 61, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 27, "endColumn": 61, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 27, "endColumn": 57, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 27, "endColumn": 57, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 43, "endColumn": 59, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 68, "endColumn": 84, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 46, "endColumn": 62, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 63, "endColumn": 92, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 33, "endColumn": 43, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 86, "endColumn": 96, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 33, "endColumn": 61, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 63, "endColumn": 99, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 71, "endColumn": 78, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 33, "endColumn": 43, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 86, "endColumn": 96, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 33, "endColumn": 61, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 63, "endColumn": 99, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 85, "endColumn": 114, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 84, "endColumn": 102, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 100, "endColumn": 129, "lineCount": 1}}, {"code": "reportOptionalIterable", "range": {"startColumn": 68, "endColumn": 97, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 43, "endColumn": 59, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 68, "endColumn": 84, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 46, "endColumn": 62, "lineCount": 1}}, {"code": "reportOptionalIterable", "range": {"startColumn": 64, "endColumn": 93, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 43, "endColumn": 59, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 68, "endColumn": 84, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 46, "endColumn": 62, "lineCount": 1}}], "./services/studio/_text_to_workflow/models/model_manager.py": [{"code": "reportArgumentType", "range": {"startColumn": 41, "endColumn": 63, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 41, "endColumn": 49, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 34, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 13, "endColumn": 34, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 49, "endColumn": 65, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 49, "endColumn": 69, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 49, "endColumn": 65, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 49, "endColumn": 69, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 49, "endColumn": 68, "lineCount": 1}}], "./services/studio/_text_to_workflow/models/output_parsers.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 32, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 28, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 32, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 32, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 97, "endColumn": 107, "lineCount": 1}}], "./services/studio/_text_to_workflow/orchestrator_dataset/converter_client/online_client.py": [{"code": "reportArgumentType", "range": {"startColumn": 28, "endColumn": 34, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 53, "endColumn": 61, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 63, "endColumn": 76, "lineCount": 1}}], "./services/studio/_text_to_workflow/orchestrator_dataset/orchestrator_dataset_download.py": [{"code": "reportAssignmentType", "range": {"startColumn": 9, "endColumn": 20, "lineCount": 1}}], "./services/studio/_text_to_workflow/semantic_diff_text/main.py": [{"code": "reportArgumentType", "range": {"startColumn": 44, "endColumn": 66, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 44, "endColumn": 66, "lineCount": 1}}], "./services/studio/_text_to_workflow/testdata_generation/testdata_generation_endpoint.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 95, "lineCount": 1}}], "./services/studio/_text_to_workflow/testdata_generation/testdata_generation_helpers.py": [{"code": "reportOperatorIssue", "range": {"startColumn": 7, "endColumn": 31, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 26, "endColumn": 37, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 20, "endColumn": 31, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 21, "endColumn": 32, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 20, "endColumn": 31, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 19, "endColumn": 30, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 16, "endColumn": 27, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 28, "endColumn": 32, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 24, "endColumn": 27, "lineCount": 1}}], "./services/studio/_text_to_workflow/testdata_generation/testdata_generation_task.py": [{"code": "reportArgumentType", "range": {"startColumn": 60, "endColumn": 82, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 84, "endColumn": 99, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 67, "endColumn": 70, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 67, "endColumn": 84, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 22, "endColumn": 47, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 28, "lineCount": 1}}, {"code": "reportOperatorIssue", "range": {"startColumn": 32, "endColumn": 76, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 21, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 57, "endColumn": 66, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 37, "endColumn": 62, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 37, "endColumn": 62, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 30, "endColumn": 55, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 30, "endColumn": 55, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 9, "endColumn": 20, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 29, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_activitysummary.py": [{"code": "reportAssignmentType", "range": {"startColumn": 8, "endColumn": 28, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 8, "endColumn": 28, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_api_workflow_expression_generation.py": [{"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 74, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_expression_generation.py": [{"code": "reportArgumentType", "range": {"startColumn": 68, "endColumn": 103, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 64, "endColumn": 98, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 64, "endColumn": 95, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 64, "endColumn": 103, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 81, "endColumn": 120, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_fix_expression.py": [{"code": "reportArgumentType", "range": {"startColumn": 61, "endColumn": 96, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 57, "endColumn": 91, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 57, "endColumn": 88, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 57, "endColumn": 96, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 74, "endColumn": 113, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_fix_workflow.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 10, "endColumn": 26, "lineCount": 1}}, {"code": "reportFunctionMemberAccess", "range": {"startColumn": 37, "endColumn": 43, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_js_invoke.py": [{"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 74, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_testdata_generation.py": [{"code": "reportCallIssue", "range": {"startColumn": 14, "endColumn": 5, "lineCount": 7}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 19, "endColumn": 62, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 14, "endColumn": 5, "lineCount": 8}}], "./services/studio/_text_to_workflow/tests/integration/test_translate_text.py": [{"code": "reportOperatorIssue", "range": {"startColumn": 11, "endColumn": 84, "lineCount": 1}}, {"code": "reportOperatorIssue", "range": {"startColumn": 15, "endColumn": 93, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_workflow_assistant_router.py": [{"code": "reportReturnType", "range": {"startColumn": 78, "endColumn": 81, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 81, "endColumn": 84, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 73, "endColumn": 76, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 70, "endColumn": 73, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 71, "endColumn": 87, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 48, "endColumn": 53, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 54, "endColumn": 59, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 1, "endColumn": 23, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 1, "endColumn": 23, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/integration/test_workflow_generation_postprocessing.py": [{"code": "reportArgumentType", "range": {"startColumn": 15, "endColumn": 44, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 15, "endColumn": 60, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/unit/test_expression_generation_helper.py": [{"code": "reportCallIssue", "range": {"startColumn": 14, "endColumn": 78, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/unit/test_uia_expansion.py": [{"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 82, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 62, "endColumn": 112, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 79, "endColumn": 129, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 58, "endColumn": 117, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 80, "endColumn": 121, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 85, "endColumn": 121, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 86, "endColumn": 124, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 82, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 57, "endColumn": 72, "lineCount": 1}}], "./services/studio/_text_to_workflow/tests/unit/test_unit_testdata_generation.py": [{"code": "reportArgumentType", "range": {"startColumn": 52, "endColumn": 67, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 52, "endColumn": 65, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/dotnet_dynamic_activities_discovery.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 32, "endColumn": 41, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 38, "endColumn": 53, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 34, "endColumn": 45, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 41, "endColumn": 55, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/embedding_model.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 25, "endColumn": 56, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 29, "endColumn": 60, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 39, "endColumn": 73, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/errors.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 30, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 30, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/helpers.py": [{"code": "reportInvalidTypeVarUse", "range": {"startColumn": 52, "endColumn": 53, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/inference/llm_gateway_model.py": [{"code": "reportArgumentType", "range": {"startColumn": 20, "endColumn": 26, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 16, "endColumn": 28, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 16, "endColumn": 26, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 16, "endColumn": 26, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/inference/llm_gateway_multi_model.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 29, "endColumn": 36, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 35, "endColumn": 46, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 94, "endColumn": 109, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 119, "endColumn": 128, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/minhash.py": [{"code": "reportCallIssue", "range": {"startColumn": 12, "endColumn": 43, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 43, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 40, "endColumn": 43, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 43, "endColumn": 46, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 49, "endColumn": 52, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 78, "endColumn": 81, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 73, "endColumn": 76, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 61, "endColumn": 64, "lineCount": 1}}, {"code": "reportInvalidTypeArguments", "range": {"startColumn": 62, "endColumn": 65, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/structured_outputs.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 37, "endColumn": 43, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/translate/translate_text_task.py": [{"code": "reportReturnType", "range": {"startColumn": 19, "endColumn": 56, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/unidiff_utils.py": [{"code": "reportArgumentType", "range": {"startColumn": 140, "endColumn": 146, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 140, "endColumn": 146, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 49, "endColumn": 54, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 49, "endColumn": 54, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 38, "endColumn": 43, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 38, "endColumn": 43, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 61, "endColumn": 66, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 44, "endColumn": 49, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/workflow_utils.py": [{"code": "reportArgumentType", "range": {"startColumn": 29, "endColumn": 45, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 48, "endColumn": 64, "lineCount": 1}}], "./services/studio/_text_to_workflow/utils/yaml_utils.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 44, "endColumn": 49, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/dataset_creation/create_from_wfgen.py": [{"code": "reportAssignmentType", "range": {"startColumn": 16, "endColumn": 20, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 8, "endColumn": 27, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 8, "endColumn": 14, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 8, "endColumn": 14, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 26, "endColumn": 41, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 26, "endColumn": 32, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 71, "endColumn": 77, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 63, "endColumn": 72, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/dataset_creation/curate_workflows.py": [{"code": "reportFunctionMemberAccess", "range": {"startColumn": 22, "endColumn": 27, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/workflow_fix_endpoint.py": [{"code": "reportAssignmentType", "range": {"startColumn": 22, "endColumn": 69, "lineCount": 1}}, {"code": "reportOptionalMemberAccess", "range": {"startColumn": 30, "endColumn": 35, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/workflow_fix_retriever.py": [{"code": "reportArgumentType", "range": {"startColumn": 96, "endColumn": 117, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 97, "endColumn": 118, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/workflow_fix_task.py": [{"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 17, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 20, "endColumn": 25, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 42, "endColumn": 58, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 20, "endColumn": 26, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 30, "endColumn": 46, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 59, "endColumn": 75, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 81, "endColumn": 140, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 117, "endColumn": 133, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 38, "endColumn": 70, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 22, "endColumn": 38, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 41, "endColumn": 47, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 68, "endColumn": 77, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 67, "endColumn": 76, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 19, "endColumn": 31, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 36, "endColumn": 70, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 43, "endColumn": 77, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_fix/workflow_fix_test.py": [{"code": "reportCallIssue", "range": {"startColumn": 12, "endColumn": 46, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 28, "endColumn": 45, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 31, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 19, "endColumn": 27, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 122, "endColumn": 127, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 8, "endColumn": 47, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/_compare_model_outputs.py": [{"code": "reportIndexIssue", "range": {"startColumn": 15, "endColumn": 21, "lineCount": 1}}, {"code": "reportIndexIssue", "range": {"startColumn": 24, "endColumn": 30, "lineCount": 1}}, {"code": "reportIndexIssue", "range": {"startColumn": 24, "endColumn": 30, "lineCount": 1}}, {"code": "reportIndexIssue", "range": {"startColumn": 24, "endColumn": 30, "lineCount": 1}}, {"code": "reportIndexIssue", "range": {"startColumn": 41, "endColumn": 47, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/_evaluate_retrieval.py": [{"code": "reportCallIssue", "range": {"startColumn": 14, "endColumn": 81, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 17, "endColumn": 9, "lineCount": 6}}, {"code": "reportCallIssue", "range": {"startColumn": 12, "endColumn": 24, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 12, "endColumn": 26, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/_tree_edit_distance.py": [{"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 83, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 83, "lineCount": 1}}, {"code": "reportIncompatibleMethodOverride", "range": {"startColumn": 8, "endColumn": 14, "lineCount": 1}}, {"code": "reportIncompatibleMethodOverride", "range": {"startColumn": 8, "endColumn": 14, "lineCount": 1}}, {"code": "reportIncompatibleMethodOverride", "range": {"startColumn": 8, "endColumn": 14, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/client_eval.py": [{"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 24, "endColumn": 31, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 19, "endColumn": 26, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 34, "endColumn": 56, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 45, "endColumn": 67, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 45, "endColumn": 67, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 45, "endColumn": 59, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 81, "endColumn": 103, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 79, "endColumn": 101, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 80, "endColumn": 102, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/demo/app.py": [{"code": "reportMissingImports", "range": {"startColumn": 7, "endColumn": 13, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 93, "endColumn": 123, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 8, "endColumn": 22, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/evaluation/draft_generation_eval.py": [{"code": "reportRedeclaration", "range": {"startColumn": 12, "endColumn": 37, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/fixyaml_task.py": [{"code": "reportArgumentType", "range": {"startColumn": 33, "endColumn": 45, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 33, "endColumn": 50, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 29, "endColumn": 50, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 47, "endColumn": 54, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 50, "endColumn": 56, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/scripts/aggregate_htmls.py": [{"code": "reportReturnType", "range": {"startColumn": 11, "endColumn": 27, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 14, "endColumn": 23, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/scripts/deduplicate.py": [{"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 4, "endColumn": 12, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 19, "endColumn": 27, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 11, "endColumn": 19, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/scripts/dump_statistics_production_data.py": [{"code": "reportArgumentType", "range": {"startColumn": 59, "endColumn": 67, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 32, "endColumn": 35, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 26, "endColumn": 34, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 18, "endColumn": 25, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 20, "endColumn": 27, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 19, "endColumn": 26, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 19, "endColumn": 26, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/sentence_embeddings/build_dataset.py": [{"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 66, "endColumn": 76, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/sentence_embeddings/eval_demos.py": [{"code": "reportCallIssue", "range": {"startColumn": 19, "endColumn": 1, "lineCount": 7}}, {"code": "reportArgumentType", "range": {"startColumn": 4, "endColumn": 11, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 20, "endColumn": 1, "lineCount": 7}}, {"code": "reportArgumentType", "range": {"startColumn": 4, "endColumn": 10, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 36, "endColumn": 60, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 69, "endColumn": 90, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 69, "endColumn": 90, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/sentence_embeddings/train.py": [{"code": "reportPrivateImportUsage", "range": {"startColumn": 42, "endColumn": 54, "lineCount": 1}}, {"code": "reportPossiblyUnboundVariable", "range": {"startColumn": 23, "endColumn": 31, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 24, "endColumn": 28, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 22, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 22, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 22, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 18, "endColumn": 22, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 21, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 21, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 21, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 17, "endColumn": 21, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/services/common/generation_settings_builder.py": [{"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 87, "endColumn": 128, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 85, "endColumn": 125, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/services/helpers/sequence_generation_helper.py": [{"code": "reportOptionalMemberAccess", "range": {"startColumn": 85, "endColumn": 90, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/services/workflow_generation_activity_retrieval_service.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 51, "endColumn": 62, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 35, "endColumn": 45, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_dynamic_activities_component.py": [{"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 40, "endColumn": 74, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 64, "endColumn": 98, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 57, "endColumn": 113, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_endpoint.py": [{"code": "reportReturnType", "range": {"startColumn": 17, "endColumn": 22, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_postprocess_component.py": [{"code": "reportArgumentType", "range": {"startColumn": 27, "endColumn": 41, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 28, "endColumn": 43, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 39, "endColumn": 53, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 44, "lineCount": 1}}, {"code": "reportTypedDictNotRequiredAccess", "range": {"startColumn": 45, "endColumn": 108, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_prompt_builder_component.py": [{"code": "reportArgumentType", "range": {"startColumn": 65, "endColumn": 78, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 55, "endColumn": 71, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 21, "endColumn": 31, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 42, "endColumn": 58, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 71, "endColumn": 81, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 24, "endColumn": 74, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 24, "endColumn": 72, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 28, "endColumn": 65, "lineCount": 1}}, {"code": "reportGeneralTypeIssues", "range": {"startColumn": 28, "endColumn": 64, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_retrievers.py": [{"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 14, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 27, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_schema.py": [{"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 12, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 28, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 22, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 14, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 19, "lineCount": 1}}, {"code": "reportRedeclaration", "range": {"startColumn": 4, "endColumn": 8, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_task.py": [{"code": "reportAttributeAccessIssue", "range": {"startColumn": 33, "endColumn": 42, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 32, "endColumn": 42, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 65, "endColumn": 81, "lineCount": 1}}, {"code": "reportAttributeAccessIssue", "range": {"startColumn": 22, "endColumn": 38, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 23, "endColumn": 40, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 21, "lineCount": 1}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 18, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 15, "endColumn": 23, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 16, "endColumn": 53, "lineCount": 1}}, {"code": "reportOptionalSubscript", "range": {"startColumn": 27, "endColumn": 35, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 52, "endColumn": 66, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 13, "lineCount": 5}}, {"code": "reportReturnType", "range": {"startColumn": 15, "endColumn": 18, "lineCount": 1}}], "./services/studio/_text_to_workflow/workflow_generation/workflow_generation_test.py": [{"code": "reportGeneralTypeIssues", "range": {"startColumn": 65, "endColumn": 73, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 29, "endColumn": 62, "lineCount": 1}}, {"code": "reportAssignmentType", "range": {"startColumn": 31, "endColumn": 66, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 40, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 47, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 12, "endColumn": 28, "lineCount": 1}}, {"code": "reportCallIssue", "range": {"startColumn": 34, "endColumn": 119, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 38, "endColumn": 118, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 51, "endColumn": 67, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 37, "endColumn": 59, "lineCount": 1}}, {"code": "reportArgumentType", "range": {"startColumn": 37, "endColumn": 51, "lineCount": 1}}]}}